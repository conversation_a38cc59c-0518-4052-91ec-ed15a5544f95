using Bdo.Ess.CtsIntegration.Certificate;
using Bdo.Ess.CtsIntegration.Constants;
using Bdo.Ess.CtsIntegration.CtsApi;
using Bdo.Ess.CtsIntegration.CtsPackageGeneration;
using Bdo.Ess.CtsIntegration.CtsPackageRequests;
using Bdo.Ess.CtsIntegration.Encryption;
using Bdo.Ess.CtsIntegration.Entities;
using Bdo.Ess.CtsIntegration.enums;
using Bdo.Ess.CtsIntegration.PackageGeneration;
using Bdo.Ess.CtsIntegration.StateMachine;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Constants.InformationExchanges;
using Bdo.Ess.Shared.Hosting.Microservices.Audit;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using System;
using System.IO;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;
using Xunit;

namespace Bdo.Ess.CtsIntegration.EntityFrameworkCore.Applications;

/// <summary>
/// Unit tests for CtsPackageGenerationAppService following ABP test patterns.
/// Tests cover package generation, unpacking, validation, and error handling.
/// Uses SQLite in-memory database for EF Core integration testing.
/// Does not mock AesManager, XmlManager, ZipManager, or DataPacketStateMachine as requested.
/// </summary>
public class CtsPackageGenerationAppService_Tests : CtsIntegrationEntityFrameworkCoreTestBase
{
    private readonly ICtsPackageGenerationAppService _appService;
    private readonly IRepository<CtsPackageRequest, Guid> _repository;
    private readonly ICtsIntegrationBlobAppService _mockBlobAppService;
    private readonly ICertificateAppService _mockCertificateAppService;
    private readonly ICountryCertificateAppService _mockCountryCertificateAppService;
    private readonly ICtsApiClient _mockCtsApiClient;
    private readonly ICtsEncryptionManager _mockEncryptionManager;
    private readonly IDistributedEventBus _mockEventBus;
    private readonly IConfiguration _mockConfiguration;
    private readonly ICurrentTenant _mockCurrentTenant;
    private readonly ILogger<CtsPackageGenerationAppService> _mockLogger;
    private readonly IAuditWebInfo _mockAuditWebInfo;

    public CtsPackageGenerationAppService_Tests()
    {
        // Get real repository from DI container for EF Core integration testing
        _repository = GetRequiredService<IRepository<CtsPackageRequest, Guid>>();
        
        // Create mocks for external dependencies
        _mockBlobAppService = Substitute.For<ICtsIntegrationBlobAppService>();
        _mockCertificateAppService = Substitute.For<ICertificateAppService>();
        _mockCountryCertificateAppService = Substitute.For<ICountryCertificateAppService>();
        _mockCtsApiClient = Substitute.For<ICtsApiClient>();
        _mockEncryptionManager = Substitute.For<ICtsEncryptionManager>();
        _mockEventBus = Substitute.For<IDistributedEventBus>();
        _mockConfiguration = Substitute.For<IConfiguration>();
        _mockCurrentTenant = Substitute.For<ICurrentTenant>();
        _mockLogger = Substitute.For<ILogger<CtsPackageGenerationAppService>>();
        _mockAuditWebInfo = Substitute.For<IAuditWebInfo>();

        // Setup mock behaviors
        SetupMockConfiguration();
        SetupMockCurrentTenant();
        SetupMockAuditWebInfo();

        // Create the app service with mocked dependencies
        _appService = new CtsPackageGenerationAppService(
            _mockBlobAppService,
            _repository,
            _mockCertificateAppService,
            _mockCountryCertificateAppService,
            _mockCtsApiClient,
            _mockEncryptionManager,
            _mockCurrentTenant,
            _mockEventBus,
            _mockConfiguration);
    }

    #region GeneratePackageAsync Tests

    [Fact]
    public async Task GeneratePackageAsync_Should_Generate_Package_When_ValidInput()
    {
        // Arrange
        var packageRequest = await SeedTestPackageRequestAsync();
        var xmlContent = CreateValidXmlContent();
        var (publicCert, privateCert) = CreateTestCertificatePair();
        var certificatePassword = "TestPassword123!";

        SetupMockDependencies(xmlContent, privateCert, publicCert, certificatePassword);

        // Act
        var result = await _appService.GeneratePackageAsync(packageRequest.Id, packageRequest.FiscalYear, 
            xmlContent, privateCert, certificatePassword, publicCert, "US");

        // Assert
        result.ShouldNotBeNullOrEmpty();
        result.ShouldContain(".zip");
        
        // Verify blob upload was called
        await _mockBlobAppService.Received().UploadFile(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<byte[]>());
    }

    [Fact]
    public async Task GeneratePackageAsync_Should_Throw_Exception_When_InvalidXml()
    {
        // Arrange
        var packageId = Guid.NewGuid();
        var invalidXml = Encoding.UTF8.GetBytes("<invalid>xml</invalid>");
        var (publicCert, privateCert) = CreateTestCertificatePair();

        // Act & Assert
        var exception = await Should.ThrowAsync<ArgumentException>(
            () => _appService.GeneratePackageAsync(packageId, 2023, invalidXml, privateCert, "password", publicCert, "US"));
        
        exception.Message.ShouldContain("Transmitting country does not match");
    }

    [Fact]
    public async Task GeneratePackageAsync_Should_Handle_Certificate_Loading_Error()
    {
        // Arrange
        var packageId = Guid.NewGuid();
        var xmlContent = CreateValidXmlContent();
        var invalidCert = new byte[] { 1, 2, 3 }; // Invalid certificate data

        // Act & Assert
        var result = await _appService.GeneratePackageAsync(packageId, 2023, xmlContent, invalidCert, "password", invalidCert, "US");
        
        result.ShouldBeEmpty(); // Should return empty string on error
    }

    [Fact]
    public async Task GeneratePackageAsync_With_PackageRequestDataDto_Should_Process_Successfully()
    {
        // Arrange
        var packageRequest = await SeedTestPackageRequestAsync();
        var dto = CreatePackageRequestDataDto(packageRequest);
        var xmlContent = CreateValidXmlContent();
        var (publicCert, privateCert) = CreateTestCertificatePair();

        SetupMockDependencies(xmlContent, privateCert, publicCert, "TestPassword123!");

        // Act
        var result = await _appService.GeneratePackageAsync(dto);

        // Assert
        result.ShouldNotBeNullOrEmpty();
        
        // Verify repository was called to get the request
        await _repository.Received().GetAsync(packageRequest.Id);
    }

    #endregion

    #region ValidateXml Tests

    [Fact]
    public void ValidateXml_Should_Pass_When_ValidXml()
    {
        // Arrange
        var validXml = CreateValidXmlContent();

        // Act & Assert
        Should.NotThrow(() => CtsPackageGenerationAppService.ValidateXml(validXml));
    }

    [Fact]
    public void ValidateXml_Should_Throw_When_NullXml()
    {
        // Act & Assert
        var exception = Should.Throw<ArgumentException>(() => CtsPackageGenerationAppService.ValidateXml(null));
        exception.Message.ShouldContain("XML payload cannot be null or empty");
    }

    [Fact]
    public void ValidateXml_Should_Throw_When_EmptyXml()
    {
        // Act & Assert
        var exception = Should.Throw<ArgumentException>(() => CtsPackageGenerationAppService.ValidateXml(new byte[0]));
        exception.Message.ShouldContain("XML payload cannot be null or empty");
    }

    [Fact]
    public void ValidateXml_Should_Throw_When_WrongTransmittingCountry()
    {
        // Arrange
        var xmlWithWrongCountry = CreateXmlWithTransmittingCountry("US");

        // Act & Assert
        var exception = Should.Throw<ArgumentException>(() => CtsPackageGenerationAppService.ValidateXml(xmlWithWrongCountry));
        exception.Message.ShouldContain("Transmitting country does not match");
    }

    [Fact]
    public void ValidateXml_Should_Throw_When_WrongMessageType()
    {
        // Arrange
        var xmlWithWrongMessageType = CreateXmlWithMessageType("CRSMessage");

        // Act & Assert
        var exception = Should.Throw<ArgumentException>(() => CtsPackageGenerationAppService.ValidateXml(xmlWithWrongMessageType));
        exception.Message.ShouldContain("Message type CRSMessage does not match expected type NTJ");
    }

    #endregion

    #region LoadSigningCertificate Tests

    [Fact]
    public void LoadSigningCertificate_Should_Load_Valid_Certificate()
    {
        // Arrange
        var (_, privateCert) = CreateTestCertificatePair();
        var password = "TestPassword123!";

        // Act
        var result = CtsPackageGenerationAppService.LoadSigningCertificate(privateCert, password);

        // Assert
        result.ShouldNotBeNull();
        result.HasPrivateKey.ShouldBeTrue();
    }

    [Fact]
    public void LoadSigningCertificate_Should_Throw_When_No_Private_Key()
    {
        // Arrange
        var (publicCert, _) = CreateTestCertificatePair();
        var password = "TestPassword123!";

        // Act & Assert
        var exception = Should.Throw<InvalidOperationException>(
            () => CtsPackageGenerationAppService.LoadSigningCertificate(publicCert, password));
        
        exception.Message.ShouldContain("Specified certificate not suitable for signing");
    }

    #endregion

    #region UnpackAsync Tests

    [Fact]
    public async Task UnpackAsync_Should_Throw_When_NullContent()
    {
        // Act & Assert
        var exception = await Should.ThrowAsync<UserFriendlyException>(
            () => _appService.UnpackAsync((byte[])null));
        
        exception.Message.ShouldContain("Zip content cannot be null or empty");
    }

    [Fact]
    public async Task UnpackAsync_Should_Throw_When_EmptyContent()
    {
        // Act & Assert
        var exception = await Should.ThrowAsync<UserFriendlyException>(
            () => _appService.UnpackAsync(new byte[0]));
        
        exception.Message.ShouldContain("Zip content cannot be null or empty");
    }

    [Fact]
    public async Task UnpackAsync_IFormFile_Should_Throw_When_NullFile()
    {
        // Act & Assert
        var exception = await Should.ThrowAsync<ArgumentException>(
            () => _appService.UnpackAsync((IFormFile)null));
        
        exception.Message.ShouldContain("File cannot be null or empty");
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task GeneratePackageAsync_Should_Update_PackageRequest_Status_On_Success()
    {
        // Arrange
        var packageRequest = await SeedTestPackageRequestAsync();
        var dto = CreatePackageRequestDataDto(packageRequest);
        var xmlContent = CreateValidXmlContent();
        var (publicCert, privateCert) = CreateTestCertificatePair();

        SetupMockDependencies(xmlContent, privateCert, publicCert, "TestPassword123!");

        // Act
        var result = await _appService.GeneratePackageAsync(dto);

        // Assert
        result.ShouldNotBeNullOrEmpty();

        // Verify the package request was updated in the database
        var updatedRequest = await _repository.GetAsync(packageRequest.Id);
        updatedRequest.PackageZipUrl.ShouldNotBeNullOrEmpty();
        updatedRequest.UploadStatus.ShouldBe(CTSUploadStatus.Uploaded);
    }

    [Fact]
    public async Task GeneratePackageAsync_Should_Update_PackageRequest_Status_On_Failure()
    {
        // Arrange
        var packageRequest = await SeedTestPackageRequestAsync();
        var dto = CreatePackageRequestDataDto(packageRequest);
        var invalidXml = Encoding.UTF8.GetBytes("<invalid>xml</invalid>");

        SetupMockDependenciesForFailure(invalidXml);

        // Act
        var result = await _appService.GeneratePackageAsync(dto);

        // Assert
        result.ShouldBeEmpty();

        // Verify the package request status was updated to failed
        var updatedRequest = await _repository.GetAsync(packageRequest.Id);
        updatedRequest.UploadStatus.ShouldBe(CTSUploadStatus.DataPacketGenerationFailed);
    }

    [Fact]
    public async Task UnpackAsync_Should_Extract_And_Validate_CtsPackage()
    {
        // Arrange
        var (publicCert, privateCert) = CreateTestCertificatePair();
        var xmlContent = CreateValidXmlContent();
        var zipContent = CreateValidCtsPackage(xmlContent, privateCert, publicCert, "TestPassword123!");

        SetupMockCertificatesForUnpacking(privateCert, "TestPassword123!");

        // Act
        var result = await _appService.UnpackAsync(zipContent);

        // Assert
        result.ShouldNotBeNull();
        result.Length.ShouldBeGreaterThan(0);

        // Verify the result contains the original XML content
        var resultString = Encoding.UTF8.GetString(result);
        resultString.ShouldContain("NTJMessage");
        resultString.ShouldContain("BS"); // Transmitting country
    }

    [Fact]
    public async Task UnpackAsync_Should_Validate_Metadata_Requirements()
    {
        // Arrange
        var (publicCert, privateCert) = CreateTestCertificatePair();
        var xmlContent = CreateValidXmlContent();
        var zipContent = CreateCtsPackageWithInvalidMetadata(xmlContent, privateCert, publicCert, "TestPassword123!");

        SetupMockCertificatesForUnpacking(privateCert, "TestPassword123!");

        // Act & Assert
        var exception = await Should.ThrowAsync<UserFriendlyException>(
            () => _appService.UnpackAsync(zipContent));

        exception.Message.ShouldContain("CTSReceiverCountryCd must be 'BS'");
    }

    #endregion

    #region Error Handling Tests

    [Fact]
    public async Task GeneratePackageAsync_Should_Handle_BlobService_Error()
    {
        // Arrange
        var packageRequest = await SeedTestPackageRequestAsync();
        var dto = CreatePackageRequestDataDto(packageRequest);

        // Setup blob service to throw exception
        _mockBlobAppService.DownloadFileBytes(Arg.Any<string>())
            .Returns(Task.FromException<byte[]>(new Exception("Blob service error")));

        // Act
        var result = await _appService.GeneratePackageAsync(dto);

        // Assert
        result.ShouldBeEmpty();

        // Verify the package request status was updated to failed
        var updatedRequest = await _repository.GetAsync(packageRequest.Id);
        updatedRequest.UploadStatus.ShouldBe(CTSUploadStatus.DataPacketGenerationFailed);
    }

    [Fact]
    public async Task GeneratePackageAsync_Should_Handle_Certificate_Service_Error()
    {
        // Arrange
        var packageRequest = await SeedTestPackageRequestAsync();
        var dto = CreatePackageRequestDataDto(packageRequest);
        var xmlContent = CreateValidXmlContent();

        SetupMockDependenciesWithCertificateError(xmlContent);

        // Act
        var result = await _appService.GeneratePackageAsync(dto);

        // Assert
        result.ShouldBeEmpty();

        // Verify the package request status was updated to failed
        var updatedRequest = await _repository.GetAsync(packageRequest.Id);
        updatedRequest.UploadStatus.ShouldBe(CTSUploadStatus.DataPacketGenerationFailed);
    }

    [Fact]
    public async Task UnpackAsync_Should_Handle_Invalid_Zip_Content()
    {
        // Arrange
        var invalidZipContent = Encoding.UTF8.GetBytes("This is not a zip file");

        // Act & Assert
        var exception = await Should.ThrowAsync<UserFriendlyException>(
            () => _appService.UnpackAsync(invalidZipContent));

        exception.Message.ShouldContain("Invalid zip file format");
    }

    [Fact]
    public async Task UnpackAsync_Should_Handle_Missing_Required_Files()
    {
        // Arrange
        var zipContent = CreateZipWithMissingFiles();

        // Act & Assert
        var exception = await Should.ThrowAsync<UserFriendlyException>(
            () => _appService.UnpackAsync(zipContent));

        exception.Message.ShouldContain("Required file not found");
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Seeds test data into the database
    /// </summary>
    private async Task<CtsPackageRequest> SeedTestPackageRequestAsync()
    {
        var entity = new CtsPackageRequest(Guid.NewGuid())
        {
            ReceiverCountryCode = "US",
            FiscalYear = 2023,
            ExchangeReason = ExchangeReason.NonCompliance,
            XmlPayloadUrl = "test-url",
            UploadStatus = CTSUploadStatus.GenerationInProgress,
            MessageRefId = "TEST-MSG-REF-001"
        };

        return await _repository.InsertAsync(entity, autoSave: true);
    }

    /// <summary>
    /// Creates a valid CtsPackageRequestDataDto for testing
    /// </summary>
    private CtsPackageRequestDataDto CreatePackageRequestDataDto(CtsPackageRequest entity)
    {
        return new CtsPackageRequestDataDto
        {
            Id = entity.Id,
            ReceiverCountryCode = entity.ReceiverCountryCode,
            FiscalYear = entity.FiscalYear,
            ExchangeReason = entity.ExchangeReason,
            XmlPayloadUrl = entity.XmlPayloadUrl,
            TenantId = Guid.NewGuid()
        };
    }

    /// <summary>
    /// Creates valid XML content for testing
    /// </summary>
    private byte[] CreateValidXmlContent()
    {
        var xml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<NTJMessage xmlns=""urn:oecd:ties:ntj:v1"">
    <MessageSpec>
        <TransmittingCountry>BS</TransmittingCountry>
        <MessageType>NTJMessage</MessageType>
        <MessageRefId>TEST-MSG-REF-001</MessageRefId>
    </MessageSpec>
</NTJMessage>";
        return Encoding.UTF8.GetBytes(xml);
    }

    /// <summary>
    /// Creates XML content with specific transmitting country
    /// </summary>
    private byte[] CreateXmlWithTransmittingCountry(string countryCode)
    {
        var xml = $@"<?xml version=""1.0"" encoding=""UTF-8""?>
<NTJMessage xmlns=""urn:oecd:ties:ntj:v1"">
    <MessageSpec>
        <TransmittingCountry>{countryCode}</TransmittingCountry>
        <MessageType>NTJMessage</MessageType>
        <MessageRefId>TEST-MSG-REF-001</MessageRefId>
    </MessageSpec>
</NTJMessage>";
        return Encoding.UTF8.GetBytes(xml);
    }

    /// <summary>
    /// Creates XML content with specific message type
    /// </summary>
    private byte[] CreateXmlWithMessageType(string messageType)
    {
        var xml = $@"<?xml version=""1.0"" encoding=""UTF-8""?>
<NTJMessage xmlns=""urn:oecd:ties:ntj:v1"">
    <MessageSpec>
        <TransmittingCountry>BS</TransmittingCountry>
        <MessageType>{messageType}</MessageType>
        <MessageRefId>TEST-MSG-REF-001</MessageRefId>
    </MessageSpec>
</NTJMessage>";
        return Encoding.UTF8.GetBytes(xml);
    }

    /// <summary>
    /// Creates a test certificate pair (public and private)
    /// </summary>
    private (byte[] publicCert, byte[] privateCert) CreateTestCertificatePair()
    {
        using var rsa = RSA.Create(2048);
        var request = new CertificateRequest("CN=Test Certificate", rsa, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
        
        var certificate = request.CreateSelfSigned(DateTimeOffset.UtcNow.AddDays(-1), DateTimeOffset.UtcNow.AddYears(1));
        
        var publicCert = certificate.Export(X509ContentType.Cert);
        var privateCert = certificate.Export(X509ContentType.Pfx, "TestPassword123!");
        
        return (publicCert, privateCert);
    }

    /// <summary>
    /// Sets up mock dependencies for successful package generation
    /// </summary>
    private void SetupMockDependencies(byte[] xmlContent, byte[] privateCert, byte[] publicCert, string password)
    {
        // Setup blob service
        _mockBlobAppService.DownloadFileBytes(Arg.Any<string>())
            .Returns(Task.FromResult(xmlContent));
        _mockBlobAppService.UploadFile(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<byte[]>())
            .Returns(Task.FromResult("test-zip-url"));

        // Setup encryption manager
        _mockEncryptionManager.DecryptStr(Arg.Any<string>())
            .Returns(callInfo => Encoding.UTF8.GetString(xmlContent));

        // Setup certificate services
        var bahamasCert = new BahamasCertificateDto
        {
            CertificateContent = Convert.ToBase64String(privateCert),
            PublicKey = Convert.ToBase64String(publicCert),
            CertificatePassword = password
        };
        _mockCertificateAppService.GetBahamasCertificateInfo()
            .Returns(Task.FromResult(bahamasCert));

        var countryCert = new CountryCertificateDto
        {
            CountryCode = "US",
            PublicKey = Convert.ToBase64String(publicCert)
        };
        _mockCountryCertificateAppService.GetByCountryCode2Async("US")
            .Returns(Task.FromResult(countryCert));
    }

    /// <summary>
    /// Sets up mock dependencies for failure scenarios
    /// </summary>
    private void SetupMockDependenciesForFailure(byte[] xmlContent)
    {
        _mockBlobAppService.DownloadFileBytes(Arg.Any<string>())
            .Returns(Task.FromResult(xmlContent));
        _mockEncryptionManager.DecryptStr(Arg.Any<string>())
            .Returns(callInfo => Encoding.UTF8.GetString(xmlContent));
    }

    /// <summary>
    /// Sets up mock dependencies with certificate service error
    /// </summary>
    private void SetupMockDependenciesWithCertificateError(byte[] xmlContent)
    {
        _mockBlobAppService.DownloadFileBytes(Arg.Any<string>())
            .Returns(Task.FromResult(xmlContent));
        _mockEncryptionManager.DecryptStr(Arg.Any<string>())
            .Returns(callInfo => Encoding.UTF8.GetString(xmlContent));
        _mockCertificateAppService.GetBahamasCertificateInfo()
            .Returns(Task.FromException<BahamasCertificateDto>(new Exception("Certificate service error")));
    }

    /// <summary>
    /// Sets up mock certificates for unpacking tests
    /// </summary>
    private void SetupMockCertificatesForUnpacking(byte[] privateCert, string password)
    {
        var bahamasCert = new BahamasCertificateDto
        {
            CertificateContent = Convert.ToBase64String(privateCert),
            CertificatePassword = password
        };
        _mockCertificateAppService.GetBahamasCertificateInfo()
            .Returns(Task.FromResult(bahamasCert));
    }

    /// <summary>
    /// Sets up mock configuration
    /// </summary>
    private void SetupMockConfiguration()
    {
        // Setup the configuration value directly - return null so default value is used
        _mockConfiguration["Cts:DebugIntermFiles"].Returns((string)null);

        // Setup configuration section for Cts
        var ctsSection = Substitute.For<IConfigurationSection>();
        ctsSection["DebugIntermFiles"].Returns((string)null);
        ctsSection.Value.Returns((string)null);
        _mockConfiguration.GetSection("Cts").Returns(ctsSection);

        // Setup other configuration values that might be used
        _mockConfiguration["Cts:UseTestCountry"].Returns((string)null);
        _mockConfiguration["Cts:Api:CountriesUseHub"].Returns((string)null);

        // For any other keys, return null so default values are used
        _mockConfiguration[Arg.Any<string>()].Returns((string)null);
    }

    /// <summary>
    /// Sets up mock current tenant
    /// </summary>
    private void SetupMockCurrentTenant()
    {
        _mockCurrentTenant.Change(Arg.Any<Guid?>())
            .Returns(Substitute.For<IDisposable>());
    }

    /// <summary>
    /// Sets up mock audit web info
    /// </summary>
    private void SetupMockAuditWebInfo()
    {
        _mockAuditWebInfo.IPAddress.Returns("127.0.0.1");
        _mockAuditWebInfo.AuditUserId.Returns("test-user-id");
    }

    /// <summary>
    /// Creates a valid CTS package for testing
    /// </summary>
    private byte[] CreateValidCtsPackage(byte[] xmlContent, byte[] privateCert, byte[] publicCert, string password)
    {
        // Create metadata
        var metadata = XmlManager.CreateMetadataFile(2023, "BS", "BS", "NTJ", "TEST-MSG-REF-001");

        // Sign the XML
        var certificate = new X509Certificate2(privateCert, password, X509KeyStorageFlags.Exportable);
        var signedXml = XmlManager.SignEnvelopedXml(certificate, xmlContent);

        // Compress the signed XML
        var compressedXml = ZipManager.CreateZipFromMemory("payload.xml", signedXml);

        // Encrypt the compressed XML
        var encryptedPayload = AesManager.EncryptFile(compressedXml, out byte[] aesKey, out byte[] aesIV);
        var encryptedKey = AesManager.EncryptAesKey(aesKey, aesIV, publicCert);

        // Create the final package
        using var packageStream = new MemoryStream();
        using (var archive = new System.IO.Compression.ZipArchive(packageStream, System.IO.Compression.ZipArchiveMode.Create, true))
        {
            // Add metadata
            var metadataEntry = archive.CreateEntry("metadata.xml");
            using (var metadataStream = metadataEntry.Open())
            {
                metadataStream.Write(metadata, 0, metadata.Length);
            }

            // Add encrypted payload
            var payloadEntry = archive.CreateEntry("payload");
            using (var payloadStream = payloadEntry.Open())
            {
                payloadStream.Write(encryptedPayload, 0, encryptedPayload.Length);
            }

            // Add encrypted key
            var keyEntry = archive.CreateEntry("key");
            using (var keyStream = keyEntry.Open())
            {
                keyStream.Write(encryptedKey, 0, encryptedKey.Length);
            }
        }

        return packageStream.ToArray();
    }

    /// <summary>
    /// Creates a CTS package with invalid metadata for testing
    /// </summary>
    private byte[] CreateCtsPackageWithInvalidMetadata(byte[] xmlContent, byte[] privateCert, byte[] publicCert, string password)
    {
        // Create invalid metadata (wrong receiver country)
        var metadata = XmlManager.CreateMetadataFile(2023, "BS", "US", "NTJ", "TEST-MSG-REF-001");

        // Create the rest normally
        var certificate = new X509Certificate2(privateCert, password, X509KeyStorageFlags.Exportable);
        var signedXml = XmlManager.SignEnvelopedXml(certificate, xmlContent);
        var compressedXml = ZipManager.CreateZipFromMemory("payload.xml", signedXml);
        var encryptedPayload = AesManager.EncryptFile(compressedXml, out byte[] aesKey, out byte[] aesIV);
        var encryptedKey = AesManager.EncryptAesKey(aesKey, aesIV, publicCert);

        using var packageStream = new MemoryStream();
        using (var archive = new System.IO.Compression.ZipArchive(packageStream, System.IO.Compression.ZipArchiveMode.Create, true))
        {
            var metadataEntry = archive.CreateEntry("metadata.xml");
            using (var metadataStream = metadataEntry.Open())
            {
                metadataStream.Write(metadata, 0, metadata.Length);
            }

            var payloadEntry = archive.CreateEntry("payload");
            using (var payloadStream = payloadEntry.Open())
            {
                payloadStream.Write(encryptedPayload, 0, encryptedPayload.Length);
            }

            var keyEntry = archive.CreateEntry("key");
            using (var keyStream = keyEntry.Open())
            {
                keyStream.Write(encryptedKey, 0, encryptedKey.Length);
            }
        }

        return packageStream.ToArray();
    }

    /// <summary>
    /// Creates a zip file with missing required files for testing
    /// </summary>
    private byte[] CreateZipWithMissingFiles()
    {
        using var zipStream = new MemoryStream();
        using (var archive = new System.IO.Compression.ZipArchive(zipStream, System.IO.Compression.ZipArchiveMode.Create, true))
        {
            // Only add metadata, missing payload and key files
            var metadataEntry = archive.CreateEntry("metadata.xml");
            using (var metadataStream = metadataEntry.Open())
            {
                var metadata = Encoding.UTF8.GetBytes("<metadata>test</metadata>");
                metadataStream.Write(metadata, 0, metadata.Length);
            }
        }

        return zipStream.ToArray();
    }

    #endregion
}

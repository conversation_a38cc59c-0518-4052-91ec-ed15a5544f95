﻿using Bdo.Ess.CtsIntegration.CtsPackageGeneration;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Application;
using Volo.Abp.AutoMapper;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.Azure;
using Volo.Abp.Modularity;
using Bdo.Ess.LookupService;
using Bdo.Ess.IdentityService;
using Bdo.Ess.CtsIntegration.PackageGeneration;
using Bdo.Ess.Shared.Hosting.Microservices.Application;
using Bdo.Ess.CtsIntegration.CtsApi;
using Bdo.Ess.EconomicSubstanceService;
using Bdo.Ess.Shared.Constants.CtsIntegration;
using Bdo.Ess.CtsIntegration.SFTP;

namespace Bdo.Ess.CtsIntegration;

[DependsOn(
    typeof(CtsIntegrationDomainModule),
    typeof(CtsIntegrationApplicationContractsModule),
    typeof(AbpDddApplicationModule),
    typeof(AbpAutoMapperModule),
    typeof(AbpBlobStoringAzureModule),
    typeof(LookupServiceHttpApiClientModule),
    typeof(EconomicSubstanceServiceHttpApiClientModule),
    typeof(EssSharedHostingMicroservicesApplicationModule),
	typeof(IdentityServiceHttpApiClientModule)
	)]
public class CtsIntegrationApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var configuration = context.Services.GetConfiguration();

		context.Services.AddTransient<ICtsApiClient, CtsApiClient>();
		// Configure CTS API settings
		context.Services.Configure<CtsApiConfiguration>(configuration.GetSection("Cts:Api"));

		context.Services.AddAutoMapperObjectMapper<CtsIntegrationApplicationModule>();
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<CtsIntegrationApplicationModule>(validate: true);
        });

        Configure<AbpBlobStoringOptions>(options =>
        {
            options.Containers.Configure<CtsIntegrationContainer>(container =>
            {
                container.UseAzure(azure =>
                {
                    azure.ConnectionString = configuration["Cts:StorageConnectionString"]!;
                    azure.ContainerName = CtsBlobContainers.ContainerName;
                    azure.CreateContainerIfNotExists = true;
                });
            });
        });

        // Register CountryCertificateAppService as itself for DI (for internal usage)
        context.Services.AddTransient<Certificate.CountryCertificateAppService>();
        context.Services.AddSingleton<ICtsSftpUploader, CtsSftpUploader>();
	}
}

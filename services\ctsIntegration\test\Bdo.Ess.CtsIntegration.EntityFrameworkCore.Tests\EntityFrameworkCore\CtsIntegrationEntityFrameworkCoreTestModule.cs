﻿using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Sqlite;
using Volo.Abp.Modularity;
using Bdo.Ess.CtsIntegration.Entities;

namespace Bdo.Ess.CtsIntegration.EntityFrameworkCore;

[DependsOn(
    typeof(CtsIntegrationApplicationTestModule),
    typeof(CtsIntegrationEntityFrameworkCoreModule),
    typeof(AbpEntityFrameworkCoreSqliteModule)
)]
public class CtsIntegrationEntityFrameworkCoreTestModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var sqliteConnection = CreateDatabaseAndGetConnection();

        Configure<AbpDbContextOptions>(options =>
        {
            options.Configure<CtsIntegrationDbContext>(c =>
            {
                c.DbContextOptions.UseSqlite(sqliteConnection);
            });
        });
    }

    private static SqliteConnection CreateDatabaseAndGetConnection()
    {
        var connection = new AbpUnitTestSqliteConnection("Data Source=:memory:");
        connection.Open();

        using var context = new SqliteCtsIntegrationDbContext(
            new DbContextOptionsBuilder<CtsIntegrationDbContext>().UseSqlite(connection).Options
        );
        context.Database.EnsureCreated();

        return connection;
    }

    private class SqliteCtsIntegrationDbContext : CtsIntegrationDbContext
    {
        public SqliteCtsIntegrationDbContext(DbContextOptions<CtsIntegrationDbContext> options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Override SQL Server-specific configurations for SQLite
            builder.Entity<BahamasCertificate>(entity =>
            {
                entity.Property(p => p.PublicKey).IsRequired().HasColumnType("TEXT");
                entity.Property(p => p.CertificateContent).IsRequired().HasColumnType("BLOB");
            });

            builder.Entity<BahamasCtsSetting>(entity =>
            {
                entity.Property(p => p.SftpSSHKey).IsRequired().HasColumnType("TEXT");
            });
        }
    }
}

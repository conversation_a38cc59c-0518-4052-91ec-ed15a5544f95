using Bdo.Ess.CtsIntegration.CtsPackageGeneration;
using Shouldly;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Xml;
using Volo.Abp.Modularity;
using Xunit;

namespace Bdo.Ess.CtsIntegration.CtsPackageGeneration
{
    /// <summary>
    /// Unit tests for XmlManager class following ABP test patterns.
    /// Tests cover XML signing, validation, metadata creation, and signature verification.
    /// </summary>
    public class XmlManager_Tests : CtsIntegrationApplicationTestBase<CtsIntegrationApplicationTestModule>
    {
        private readonly string _testXmlContent = @"<?xml version=""1.0"" encoding=""utf-8""?>
<TestRoot xmlns=""http://test.namespace"">
    <MessageRefId>TEST-MSG-001</MessageRefId>
    <MessageType>CTS</MessageType>
    <TransmittingCountry>BS</TransmittingCountry>
    <ReceivingCountry>US</ReceivingCountry>
    <TestData>Sample test data</TestData>
</TestRoot>";

        private readonly string _notificationXmlContent = @"<?xml version=""1.0"" encoding=""utf-8""?>
<CTSStatusMessage xmlns=""urn:oecd:ties:ctsstatusnotification:v1"">
    <OriginalCTSTransmissionId>ORIG-TRANS-001</OriginalCTSTransmissionId>
    <SenderFileId>SENDER-FILE-001</SenderFileId>
    <CTSCommunicationTypeCd>NTJ</CTSCommunicationTypeCd>
    <FileCreateTs>2023-12-01T10:30:00Z</FileCreateTs>
</CTSStatusMessage>";

        #region Constants Tests

        [Fact]
        public void Constants_Should_Have_Correct_Values()
        {
            // Assert
            XmlManager.NodeNameMessageRefId.ShouldBe("MessageRefId");
            XmlManager.NodeNameMessageType.ShouldBe("MessageType");
            XmlManager.NodeNameTransmittingCountry.ShouldBe("TransmittingCountry");
            XmlManager.NodeNameReceivingCountry.ShouldBe("ReceivingCountry");
        }

        #endregion

        #region SignEnvelopedXml Tests

        [Fact]
        public void SignEnvelopedXml_Should_Return_SignedXml_When_ValidInputs()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);

            // Act
            var signedXml = XmlManager.SignEnvelopedXml(certificate, xmlBytes);

            // Assert
            signedXml.ShouldNotBeNull();
            signedXml.Length.ShouldBeGreaterThan(xmlBytes.Length);
            
            var signedXmlString = Encoding.UTF8.GetString(signedXml);
            signedXmlString.ShouldContain("Signature");
            signedXmlString.ShouldContain("SignedInfo");
            signedXmlString.ShouldContain("SignatureValue");
            signedXmlString.ShouldContain("KeyInfo");
        }

        [Fact]
        public void SignEnvelopedXml_Should_Throw_Exception_When_XmlIsNull()
        {
            // Arrange
            var certificate = CreateTestCertificate();

            // Act & Assert
            Should.Throw<InvalidDataException>(() =>
                XmlManager.SignEnvelopedXml(certificate, null!));
        }

        [Fact]
        public void SignEnvelopedXml_Should_Throw_Exception_When_XmlIsEmpty()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var emptyXml = new byte[0];

            // Act & Assert
            Should.Throw<InvalidDataException>(() => 
                XmlManager.SignEnvelopedXml(certificate, emptyXml));
        }

        [Fact]
        public void SignEnvelopedXml_Should_Throw_Exception_When_XmlIsInvalid()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var invalidXml = Encoding.UTF8.GetBytes("This is not valid XML");

            // Act & Assert
            Should.Throw<XmlException>(() => 
                XmlManager.SignEnvelopedXml(certificate, invalidXml));
        }

        [Fact]
        public void SignEnvelopedXml_Should_Preserve_XmlDeclaration()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);

            // Act
            var signedXml = XmlManager.SignEnvelopedXml(certificate, xmlBytes);

            // Assert
            var signedXmlString = Encoding.UTF8.GetString(signedXml);
            signedXmlString.ShouldStartWith("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        }

        [Fact]
        public void SignEnvelopedXml_Should_Include_DataObject_With_CTS_Id()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);

            // Act
            var signedXml = XmlManager.SignEnvelopedXml(certificate, xmlBytes);

            // Assert
            var signedXmlString = Encoding.UTF8.GetString(signedXml);
            signedXmlString.ShouldContain("Object Id=\"CTS\"");
        }

        #endregion

        #region CheckReceiverCode Tests

        [Fact]
        public void CheckReceiverCode_Should_Return_ReceivingCountry_When_Present()
        {
            // Arrange
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);

            // Act
            var receiverCode = XmlManager.CheckReceiverCode(xmlBytes);

            // Assert
            receiverCode.ShouldBe("US");
        }

        [Fact]
        public void CheckReceiverCode_Should_Return_Empty_When_ReceivingCountry_NotPresent()
        {
            // Arrange
            var xmlWithoutReceiver = @"<?xml version=""1.0"" encoding=""utf-8""?>
<TestRoot xmlns=""http://test.namespace"">
    <MessageRefId>TEST-MSG-001</MessageRefId>
    <TransmittingCountry>BS</TransmittingCountry>
</TestRoot>";
            var xmlBytes = Encoding.UTF8.GetBytes(xmlWithoutReceiver);

            // Act
            var receiverCode = XmlManager.CheckReceiverCode(xmlBytes);

            // Assert
            receiverCode.ShouldBe("");
        }

        #endregion

        #region CheckNotification Tests

        [Fact]
        public void CheckNotification_Should_Return_AllValues_When_Present()
        {
            // Arrange
            var xmlBytes = Encoding.UTF8.GetBytes(_notificationXmlContent);

            // Act
            var notificationValues = XmlManager.CheckNotification(xmlBytes);

            // Assert
            notificationValues.ShouldNotBeNull();
            notificationValues.Length.ShouldBe(4);
            notificationValues[0].ShouldBe("ORIG-TRANS-001"); // OriginalCTSTransmissionId
            notificationValues[1].ShouldBe("SENDER-FILE-001"); // SenderFileId
            notificationValues[2].ShouldBe("NTJ"); // CTSCommunicationTypeCd
            notificationValues[3].ShouldBe("2023-12-01T10:30:00Z"); // FileCreateTs
        }

        [Fact]
        public void CheckNotification_Should_Return_EmptyValues_When_ElementsNotPresent()
        {
            // Arrange
            var xmlWithoutElements = @"<?xml version=""1.0"" encoding=""utf-8""?>
<CTSStatusMessage xmlns=""urn:oecd:ties:ctsstatusnotification:v1"">
</CTSStatusMessage>";
            var xmlBytes = Encoding.UTF8.GetBytes(xmlWithoutElements);

            // Act
            var notificationValues = XmlManager.CheckNotification(xmlBytes);

            // Assert
            notificationValues.ShouldNotBeNull();
            notificationValues.Length.ShouldBe(4);
            notificationValues[0].ShouldBe(""); // OriginalCTSTransmissionId
            notificationValues[1].ShouldBe(""); // SenderFileId
            notificationValues[2].ShouldBe(""); // CTSCommunicationTypeCd
            notificationValues[3].ShouldBe(""); // FileCreateTs
        }

        #endregion

        #region CheckElement Tests

        [Fact]
        public void CheckElement_Should_Return_FirstElement_When_CheckFirst_True()
        {
            // Arrange
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);

            // Act
            var messageRefId = XmlManager.CheckElement(xmlBytes, "MessageRefId", true);

            // Assert
            messageRefId.ShouldBe("TEST-MSG-001");
        }

        [Fact]
        public void CheckElement_Should_Return_Empty_When_ElementNotFound()
        {
            // Arrange
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);

            // Act
            var nonExistentElement = XmlManager.CheckElement(xmlBytes, "NonExistentElement", true);

            // Assert
            nonExistentElement.ShouldBe("");
        }

        [Fact]
        public void CheckElement_XmlDocument_Should_Work_Correctly()
        {
            // Arrange
            var xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(_testXmlContent);

            // Act
            var messageType = XmlManager.CheckElement(xmlDoc, "MessageType", true);

            // Assert
            messageType.ShouldBe("CTS");
        }

        #endregion

        #region CreateMetadataFile Tests

        [Fact]
        public void CreateMetadataFile_Should_Return_ValidXml_When_ValidInputs()
        {
            // Arrange
            int taxYear = 2023;
            string senderCode = "BS";
            string receiverCode = "US";
            string commTypeCode = "NTJ";
            string messRefId = "TEST-MSG-001";

            // Act
            var metadataBytes = XmlManager.CreateMetadataFile(taxYear, senderCode, receiverCode, commTypeCode, messRefId);

            // Assert
            metadataBytes.ShouldNotBeNull();
            metadataBytes.Length.ShouldBeGreaterThan(0);

            var metadataXml = Encoding.UTF8.GetString(metadataBytes);
            metadataXml.ShouldContain("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
            metadataXml.ShouldContain("CTSSenderFileMetadata");
            metadataXml.ShouldContain(senderCode);
            metadataXml.ShouldContain(receiverCode);
            metadataXml.ShouldContain(commTypeCode);
            metadataXml.ShouldContain(messRefId);
            metadataXml.ShouldContain(taxYear.ToString());
        }

        [Fact]
        public void CreateMetadataFile_Should_Include_CurrentDateTime()
        {
            // Arrange
            var beforeTime = DateTime.UtcNow.AddMinutes(-1);
            int taxYear = 2023;
            string senderCode = "BS";
            string receiverCode = "US";
            string commTypeCode = "NTJ";
            string messRefId = "TEST-MSG-001";

            // Act
            var metadataBytes = XmlManager.CreateMetadataFile(taxYear, senderCode, receiverCode, commTypeCode, messRefId);
            var afterTime = DateTime.UtcNow.AddMinutes(1);

            // Assert
            metadataBytes.ShouldNotBeNull();
            metadataBytes.Length.ShouldBeGreaterThan(0);

            var metadataXml = Encoding.UTF8.GetString(metadataBytes);
            metadataXml.ShouldContain("FileCreateTs");

            // Debug: Print the actual XML content to understand the issue
            System.Diagnostics.Debug.WriteLine($"Metadata XML: {metadataXml}");

            // Check if the XML starts with BOM or has invalid characters
            if (metadataBytes.Length >= 3 && metadataBytes[0] == 0xEF && metadataBytes[1] == 0xBB && metadataBytes[2] == 0xBF)
            {
                // Remove BOM if present
                metadataXml = Encoding.UTF8.GetString(metadataBytes, 3, metadataBytes.Length - 3);
            }

            // Verify it's valid XML
            var xmlDoc = new XmlDocument();
            Should.NotThrow(() => xmlDoc.LoadXml(metadataXml));

            // Extract the timestamp and verify it's within the expected range
            var timestampNodes = xmlDoc.GetElementsByTagName("FileCreateTs");
            timestampNodes.Count.ShouldBeGreaterThan(0);
            var timestampNode = timestampNodes[0];
            timestampNode.ShouldNotBeNull();

            var timestampText = timestampNode!.InnerText.Replace("Z", "");
            var timestamp = DateTime.Parse(timestampText);

            timestamp.ShouldBeGreaterThan(beforeTime);
            timestamp.ShouldBeLessThan(afterTime);
        }

        [Fact]
        public void CreateMetadataFile_Should_Handle_NullTaxYear()
        {
            // Arrange
            int? taxYear = null;
            string senderCode = "BS";
            string receiverCode = "US";
            string commTypeCode = "NTJ";
            string messRefId = "TEST-MSG-001";

            // Act
            var metadataBytes = XmlManager.CreateMetadataFile(taxYear, senderCode, receiverCode, commTypeCode, messRefId);

            // Assert
            metadataBytes.ShouldNotBeNull();
            var metadataXml = Encoding.UTF8.GetString(metadataBytes);
            metadataXml.ShouldContain("CTSSenderFileMetadata");
        }

        #endregion

        #region CheckSignature Tests

        [Fact]
        public void CheckSignature_Should_Return_True_When_ValidSignature()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);
            var signedXml = XmlManager.SignEnvelopedXml(certificate, xmlBytes);
            var publicKeyBytes = certificate.Export(X509ContentType.Cert);

            // Act
            var isValid = XmlManager.CheckSignature(signedXml, publicKeyBytes);

            // Assert
            isValid.ShouldBeTrue();
        }

        [Fact]
        public void CheckSignature_Should_Return_False_When_InvalidSignature()
        {
            // Arrange
            var certificate1 = CreateTestCertificate();
            var certificate2 = CreateTestCertificate(); // Different certificate
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);
            var signedXml = XmlManager.SignEnvelopedXml(certificate1, xmlBytes);
            var wrongPublicKeyBytes = certificate2.Export(X509ContentType.Cert);

            // Act
            var isValid = XmlManager.CheckSignature(signedXml, wrongPublicKeyBytes);

            // Assert
            isValid.ShouldBeFalse();
        }

        [Fact]
        public void CheckSignature_Should_Throw_Exception_When_NoSignature()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent); // Unsigned XML
            var publicKeyBytes = certificate.Export(X509ContentType.Cert);

            // Act & Assert
            Should.Throw<InvalidDataException>(() =>
                XmlManager.CheckSignature(xmlBytes, publicKeyBytes));
        }

        [Fact]
        public void CheckSignature_String_Should_Work_Correctly()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);
            var signedXml = XmlManager.SignEnvelopedXml(certificate, xmlBytes);
            var publicKeyString = Convert.ToBase64String(certificate.Export(X509ContentType.Cert));

            // Act
            var isValid = XmlManager.CheckSignature(signedXml, publicKeyString);

            // Assert
            isValid.ShouldBeTrue();
        }

        #endregion

        #region VerifyEnvelopedXmlSignature Tests

        [Fact]
        public void VerifyEnvelopedXmlSignature_Should_Return_True_When_ValidSignature()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);
            var signedXml = XmlManager.SignEnvelopedXml(certificate, xmlBytes);

            // Act
            var isValid = XmlManager.VerifyEnvelopedXmlSignature(signedXml, certificate);

            // Assert
            isValid.ShouldBeTrue();
        }

        [Fact]
        public void VerifyEnvelopedXmlSignature_Should_Return_False_When_InvalidSignature()
        {
            // Arrange
            var certificate1 = CreateTestCertificate();
            var certificate2 = CreateTestCertificate();
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);
            var signedXml = XmlManager.SignEnvelopedXml(certificate1, xmlBytes);

            // Act
            var isValid = XmlManager.VerifyEnvelopedXmlSignature(signedXml, certificate2);

            // Assert
            isValid.ShouldBeFalse();
        }

        [Fact]
        public void VerifyEnvelopedXmlSignature_Should_Return_False_When_Exception_Occurs()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var invalidXml = Encoding.UTF8.GetBytes("Invalid XML content");

            // Act
            var isValid = XmlManager.VerifyEnvelopedXmlSignature(invalidXml, certificate);

            // Assert
            isValid.ShouldBeFalse();
        }

        #endregion

        #region RemoveSignatureFromXml Tests

        [Fact]
        public void RemoveSignatureFromXml_Should_Return_OriginalXml_When_SignedXml()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);
            var signedXml = XmlManager.SignEnvelopedXml(certificate, xmlBytes);

            // Act
            var originalXml = XmlManager.RemoveSignatureFromXml(signedXml);

            // Assert
            originalXml.ShouldNotBeNull();
            originalXml.Length.ShouldBeLessThan(signedXml.Length);

            var originalXmlString = Encoding.UTF8.GetString(originalXml);
            originalXmlString.ShouldNotContain("Signature");
            originalXmlString.ShouldNotContain("SignedInfo");
            originalXmlString.ShouldNotContain("SignatureValue");
            originalXmlString.ShouldContain("TestRoot");
            originalXmlString.ShouldContain("MessageRefId");
        }

        [Fact]
        public void RemoveSignatureFromXml_Should_Return_OriginalXml_When_UnsignedXml()
        {
            // Arrange
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);

            // Act
            var result = XmlManager.RemoveSignatureFromXml(xmlBytes);

            // Assert
            result.ShouldNotBeNull();
            var resultString = Encoding.UTF8.GetString(result);
            resultString.ShouldContain("TestRoot");
            resultString.ShouldContain("MessageRefId");
        }

        [Fact]
        public void RemoveSignatureFromXml_Should_Return_OriginalContent_When_InvalidXml()
        {
            // Arrange
            var invalidXml = Encoding.UTF8.GetBytes("This is not valid XML");

            // Act
            var result = XmlManager.RemoveSignatureFromXml(invalidXml);

            // Assert
            result.ShouldBe(invalidXml);
        }

        [Fact]
        public void RemoveSignatureFromXml_Should_Handle_MultipleSignatures()
        {
            // Arrange - Create XML with multiple signature elements
            var xmlWithMultipleSignatures = @"<?xml version=""1.0"" encoding=""utf-8""?>
<TestRoot>
    <Signature xmlns=""http://www.w3.org/2000/09/xmldsig#"">
        <SignedInfo>Test1</SignedInfo>
    </Signature>
    <Data>Test Data</Data>
    <Signature xmlns=""http://www.w3.org/2000/09/xmldsig#"">
        <SignedInfo>Test2</SignedInfo>
    </Signature>
</TestRoot>";
            var xmlBytes = Encoding.UTF8.GetBytes(xmlWithMultipleSignatures);

            // Act
            var result = XmlManager.RemoveSignatureFromXml(xmlBytes);

            // Assert
            var resultString = Encoding.UTF8.GetString(result);
            resultString.ShouldNotContain("Signature");
            resultString.ShouldContain("Data");
            resultString.ShouldContain("Test Data");
        }

        #endregion

        #region CheckMetadata Tests

        [Fact]
        public void CheckMetadata_Should_Handle_ValidMetadata()
        {
            // Arrange
            var validMetadata = XmlManager.CreateMetadataFile(2023, "BS", "US", "NTJ", "TEST-001");

            // Act & Assert
            // The method should either return validation messages or throw FileNotFoundException
            // depending on whether XSD files exist in the test environment
            var result = Record.Exception(() =>
            {
                var validationResult = XmlManager.CheckMetadata(validMetadata);
                validationResult.ShouldNotBeNull();
            });

            // Accept either successful validation or FileNotFoundException
            if (result != null)
            {
                result.ShouldBeOfType<FileNotFoundException>();
                result.Message.ShouldContain("schema file not found");
            }
        }

        [Fact]
        public void CheckMetadata_Should_Handle_InvalidMetadata()
        {
            // Arrange
            var invalidMetadata = Encoding.UTF8.GetBytes(@"<?xml version=""1.0"" encoding=""utf-8""?>
<InvalidRoot>
    <SomeElement>Invalid content</SomeElement>
</InvalidRoot>");

            // Act & Assert
            // The method should either return validation errors or throw FileNotFoundException
            var result = Record.Exception(() =>
            {
                var validationResult = XmlManager.CheckMetadata(invalidMetadata);
                validationResult.ShouldNotBeNull();
            });

            // Accept either validation errors or FileNotFoundException
            if (result != null)
            {
                result.ShouldBeOfType<FileNotFoundException>();
                result.Message.ShouldContain("schema file not found");
            }
        }

        #endregion

        #region Integration Tests

        [Fact]
        public void SignAndVerify_Integration_Should_Work_EndToEnd()
        {
            // Arrange
            var certificate = CreateTestCertificate();
            var xmlBytes = Encoding.UTF8.GetBytes(_testXmlContent);

            // Act - Sign the XML
            var signedXml = XmlManager.SignEnvelopedXml(certificate, xmlBytes);

            // Act - Verify the signature
            var isValid = XmlManager.VerifyEnvelopedXmlSignature(signedXml, certificate);

            // Act - Remove the signature
            var originalXml = XmlManager.RemoveSignatureFromXml(signedXml);

            // Assert
            isValid.ShouldBeTrue();
            originalXml.ShouldNotBeNull();

            var originalXmlString = Encoding.UTF8.GetString(originalXml);
            originalXmlString.ShouldContain("TestRoot");
            originalXmlString.ShouldNotContain("Signature");
        }

        [Fact]
        public void ElementChecking_Integration_Should_Work_WithDifferentXmlStructures()
        {
            // Arrange
            var xmlWithMultipleElements = @"<?xml version=""1.0"" encoding=""utf-8""?>
<Root>
    <MessageRefId>MSG-001</MessageRefId>
    <MessageRefId>MSG-002</MessageRefId>
    <TransmittingCountry>BS</TransmittingCountry>
    <ReceivingCountry>US</ReceivingCountry>
    <ReceivingCountry>CA</ReceivingCountry>
</Root>";
            var xmlBytes = Encoding.UTF8.GetBytes(xmlWithMultipleElements);

            // Act
            var firstMessageRef = XmlManager.CheckElement(xmlBytes, "MessageRefId", true);
            var allMessageRefs = XmlManager.CheckElement(xmlBytes, "MessageRefId", false);
            var receiverCode = XmlManager.CheckReceiverCode(xmlBytes);

            // Assert
            firstMessageRef.ShouldBe("MSG-001");
            allMessageRefs.ShouldBe("MSG-001,MSG-002");
            // CheckReceiverCode calls CheckElement with checkFirst=false, so it returns all values
            receiverCode.ShouldBe("US,CA");
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Creates a test certificate for testing purposes
        /// </summary>
        /// <returns>X509Certificate2 for testing</returns>
        private X509Certificate2 CreateTestCertificate()
        {
            using var rsa = RSA.Create(2048);
            var certRequest = new CertificateRequest(
                "CN=Test Certificate", 
                rsa, 
                HashAlgorithmName.SHA256, 
                RSASignaturePadding.Pkcs1);

            // Add key usage extensions
            certRequest.CertificateExtensions.Add(
                new X509KeyUsageExtension(
                    X509KeyUsageFlags.DataEncipherment | X509KeyUsageFlags.KeyEncipherment | X509KeyUsageFlags.DigitalSignature, 
                    false));

            var certificate = certRequest.CreateSelfSigned(
                DateTimeOffset.Now.AddDays(-1), 
                DateTimeOffset.Now.AddDays(365));

            return certificate;
        }

        #endregion
    }
}

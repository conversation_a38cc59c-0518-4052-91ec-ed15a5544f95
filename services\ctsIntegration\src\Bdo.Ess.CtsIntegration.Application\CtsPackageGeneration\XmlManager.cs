﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Schema;
using Bdo.Ess.Shared.Utility.Utils;

namespace Bdo.Ess.CtsIntegration.CtsPackageGeneration
{
    public static class XmlManager
    {
        public const string NodeNameMessageRefId = "MessageRefId";
        public const string NodeNameMessageType = "MessageType";
        public const string NodeNameTransmittingCountry = "TransmittingCountry";
        public const string NodeNameReceivingCountry = "ReceivingCountry";

        public static byte[] SignEnvelopedXml(X509Certificate2 certificate, byte[] xml)
        {
            if (xml == null || xml.Length == 0)
            {
                throw new InvalidDataException("Xml Payload is empty, nothing to sign!");
            }

            // create new XmlDocument from stream
            XmlDocument doc = XmlHelper.LoadToXmlDocument(xml);

            // create new SignedXml from XmlDocument
            SignedXml signed = GenerateSignedXml(doc, certificate);

            // create transform (for canonicalization method & reference)
            XmlDsigExcC14NTransform transform = new XmlDsigExcC14NTransform();
            (signed.SignedInfo ?? new()).CanonicalizationMethod = transform.Algorithm;

            // get nodes (use XPath to include CTS declaration)
            if (doc.DocumentElement == null)
            {
                throw new InvalidDataException("The XML document does not have a root element.");
            }
            XmlNodeList nodes = doc.DocumentElement.SelectNodes("/*")!;

            // define data object
            DataObject dataObject = new() { Data = nodes, Id = "CTS" };

            // add the data we are signing as a sub-element (object) of the signature element
            signed.AddObject(dataObject);

            // create reference
            Reference reference = new Reference(string.Format("#{0}", dataObject.Id));
            reference.AddTransform(transform);

            // SHA-256 digest
            reference.DigestMethod = RSAPKCS1SHA256SignatureDescription.ReferenceDigestMethod;

            // add reference to document
            signed.AddReference(reference);

            // include KeyInfo object & compute signature
            signed.KeyInfo = CreateKeyInfoFromCertificate(certificate);
            signed.ComputeSignature();

            // get signature
            XmlElement xmlDigitalSignature = signed.GetXml();

            // XML declaration
            string xmlDeclaration = string.Empty;

            if (doc.FirstChild is XmlDeclaration)
            {
                // include declaration
                xmlDeclaration = doc.FirstChild.OuterXml;
            }

            // return signature as byte array
            return Encoding.UTF8.GetBytes(string.Concat(xmlDeclaration, xmlDigitalSignature.OuterXml));
        }

        public static string CheckReceiverCode(byte[] xmlFileContent)
        {
            return CheckElement(xmlFileContent, NodeNameReceivingCountry, false);
        }

        public static string[] CheckNotification(byte[] xmlFileContent)
        {
            XmlDocument doc = XmlHelper.LoadToXmlDocument(xmlFileContent);
            string[] notificationValues = new string[4];
            notificationValues[0] = CheckElement(doc, "OriginalCTSTransmissionId", true);
            notificationValues[1] = CheckElement(doc, "SenderFileId", true);
            notificationValues[2] = CheckElement(doc, "CTSCommunicationTypeCd", true);
            notificationValues[3] = CheckElement(doc, "FileCreateTs", true);
            return notificationValues;
        }

        public static string CheckElement(byte[] xmlFileContent, string elementName, bool checkFirst = true)
        {
            return XmlHelper.CheckElement(xmlFileContent, elementName, checkFirst);
        }

        public static string CheckElement(XmlDocument doc, string elementName, bool checkFirst = true)
        {
            return XmlHelper.CheckElement(doc, elementName, checkFirst);
        }

        public static byte[] CreateMetadataFile(int? taxYear, string senderCode, string receiverCode, string commTypeCode, string messRefId)
        {
            //Start creating XML metadata
            var fileCreationDateTime = DateTime.UtcNow.ToString("yyyy'-'MM'-'dd'T'HH':'mm':'ssZ");

            // Create an XmlWriterSettings object with the correct options.
            XmlWriterSettings settings = new()
            {
                Indent = true,
                IndentChars = ("\t"),
                OmitXmlDeclaration = false,
                NewLineHandling = NewLineHandling.Replace,
                CloseOutput = true
            };

            using (MemoryStream memoryStream = new MemoryStream())
            {
                // Create the XmlWriter object and write some content.
                var writer = XmlWriter.Create(memoryStream, settings);
                writer.WriteStartElement("CTSSenderFileMetadata", "urn:oecd:ctssenderfilemetadata");
                writer.WriteAttributeString("xmlns", "xsi", null, "http://www.w3.org/2001/XMLSchema-instance");
                writer.WriteStartElement("CTSSenderCountryCd");
                writer.WriteString(senderCode);
                writer.WriteEndElement();
                writer.WriteStartElement("CTSReceiverCountryCd");
                writer.WriteString(receiverCode);
                writer.WriteEndElement();
                writer.WriteStartElement("CTSCommunicationTypeCd");
                writer.WriteString(commTypeCode);
                writer.WriteEndElement();
                writer.WriteStartElement("SenderFileId");
                writer.WriteString(senderCode + "_" + receiverCode + "_" + commTypeCode + "_" + messRefId);
                writer.WriteEndElement();
                writer.WriteStartElement("FileFormatCd");
                writer.WriteString("XML");
                writer.WriteEndElement();
                writer.WriteStartElement("BinaryEncodingSchemeCd");
                writer.WriteString("NONE");
                writer.WriteEndElement();
                writer.WriteStartElement("FileCreateTs");
                writer.WriteString(fileCreationDateTime);
                writer.WriteEndElement();
                //Only write out the TaxYear if something was selected
                if (taxYear != null)
                {
                    writer.WriteStartElement("TaxYear");
                    writer.WriteString(taxYear.ToString());
                    writer.WriteEndElement();
                }

                //Close the XmlTextWriter.
                writer.WriteEndDocument();
                writer.Close();
                writer.Flush();

                return memoryStream.ToArray();
            }
        }

        public static string CheckMetadata(byte[] fileContent)
        {
            // Get the application base directory
            string applicationDirectory = AppDomain.CurrentDomain.BaseDirectory;

            // Define exact file paths
            string crsFile = Path.Combine(applicationDirectory, @"CtsPackageGeneration\Xsd", "CTS-SenderFileMetadata-2.0.xsd");
            string isoFile = Path.Combine(applicationDirectory, @"CtsPackageGeneration\Xsd", "isoctstypes_v1.1.xsd");

            // Check if files exist
            if (!File.Exists(crsFile))
            {
                throw new FileNotFoundException($"Metadata schema file not found: {crsFile}");
            }

            if (!File.Exists(isoFile))
            {
                throw new FileNotFoundException($"IsoCts schema file not found: {isoFile}");
            }

            // Create schema set with the XSD files
            XmlSchemaSet schemas = new();
            schemas.Add("urn:oecd:ctssenderfilemetadata", crsFile);
            schemas.Add("urn:oecd:ties:isoctstypes:v1", isoFile);

            // Validate the document
            using var ms = new MemoryStream(fileContent);
            XDocument doc = XDocument.Load(ms);
            string msg = "";
            doc.Validate(schemas, (o, e) =>
            {
                msg += e.Message + Environment.NewLine;
            });

            return msg;
        }

        public static bool CheckSignature(byte[] signedXml, string publicKeyFileContent)
        {
            var xmlDoc = XmlHelper.LoadToXmlDocument(signedXml);
            //convert string value to byte array

            byte[] publicKeyBytes = Encoding.UTF8.GetBytes(publicKeyFileContent);
            return CheckSignature(xmlDoc, publicKeyBytes);
        }

        public static bool CheckSignature(XmlDocument xmlDoc, byte[] publicKeyBytes)
        {
            // get signature node
            XmlNodeList nodeList = xmlDoc.GetElementsByTagName("Signature", "*");
            if (nodeList.Count != 1)
            {
                // invalid file
                throw new InvalidDataException("Signature is missing or multiple signatures found!");
            }

            // create SignedXml and load it with data
            SignedXml signed = new SignedXml(xmlDoc);
            // With this null-safe version:
            var signatureElement = nodeList[0] as XmlElement ?? throw new InvalidDataException("Signature element is missing or not an XmlElement.");

            signed.LoadXml(signatureElement);
            XmlElement root = xmlDoc.DocumentElement!;

            // check the reference in the signature
            CheckSignatureReference(signed, root);

            // load the public key and check the signature
            X509Certificate2 cert = new(publicKeyBytes);

            using RSA? rsa = cert.GetRSAPublicKey() ?? throw new InvalidDataException("Certificate does not have a valid RSA public key.");
            return signed.CheckSignature(rsa);
        }

        public static bool CheckSignature(byte[] signedXmlBytes, byte[] publicKeyBytes)
        {
            using MemoryStream stream = new(signedXmlBytes);
            // go to the beginning of the stream
            stream.Flush();
            stream.Position = 0;

            // create new XmlDocument from stream
            XmlDocument doc = new XmlDocument() { PreserveWhitespace = true };
            doc.Load(stream);

            return CheckSignature(doc, publicKeyBytes);
        }
       
        private static SignedXml GenerateSignedXml(XmlDocument doc, X509Certificate2 certificate)
        {
            // create new SignedXml from XmlDocument
            SignedXml signed = new(doc)
            {
                // set signing key and signature method for SHA-256
                SigningKey = GetSigningRsaKeyFromCertificate(certificate)
            };
            (signed.SignedInfo ?? new()).SignatureMethod = RSAPKCS1SHA256SignatureDescription.SignatureMethod;

            return signed;
        }

        private static RSACryptoServiceProvider GetSigningRsaKeyFromCertificate(X509Certificate2 certificate)
        {
            // Use the new API to get the RSA private key
            RSA? rsa = certificate.GetRSAPrivateKey() ?? throw new InvalidOperationException("Certificate does not have an RSA private key.");

            // If the key is already an RSACryptoServiceProvider, return it
            if (rsa is RSACryptoServiceProvider csp)
                return csp;

            // Otherwise, export the parameters and import into a new RSACryptoServiceProvider
            RSAParameters parameters = rsa.ExportParameters(true);
            var key = new RSACryptoServiceProvider(4096);
            key.ImportParameters(parameters);
            return key;
        }

        private static KeyInfo CreateKeyInfoFromCertificate(X509Certificate2 certificate)
        {
            // create KeyInfoX509Data object & include certificate subject
            KeyInfoX509Data kiData = new KeyInfoX509Data(certificate);
            kiData.AddSubjectName(certificate.Subject);

            // create KeyInfo object with specified KeyInfoX509Data
            KeyInfo keyInfo = new KeyInfo();
            keyInfo.AddClause(kiData);

            return keyInfo;
        }

        private static void CheckSignatureReference(SignedXml signedXml, XmlElement xmlElement)
        {
            //if no reference at all is found, there is a problem
            if (signedXml?.SignedInfo?.References.Count == 0)
            {
                throw new InvalidDataException("No reference was found in XML signature");
            }

            //if there is more than one reference, there is a problem
            if (signedXml?.SignedInfo?.References.Count != 1)
            {
                throw new InvalidDataException("Multiple references for XML signatures are not allowed");
            }

            var reference = signedXml.SignedInfo.References[0] as Reference;
            var id = reference?.Uri?[1..];
            var idElement = signedXml.GetIdElement(xmlElement.OwnerDocument, id ?? "");
            string signedReference = "";

            //the reference in the XML will be compared to the reference in the signature, if no match, there is a problem
            if (idElement == null)
            {
                XmlNodeList objectNode = xmlElement.GetElementsByTagName("Object", "*");
                //If we dont fine the id above, we will pull the Object element from the file
                if (objectNode.Count == 1)
                {
                    //Create a new attribute.
                    XmlNode? testroot = objectNode[0];
                    if (testroot != null && testroot.Attributes != null)
                    {
                        signedReference = testroot.Attributes[0].Value.ToString();
                    }
                }

                //Check the reference from the XML and see if it matches the signature, if it still doesn't there is a problem
                if (id != signedReference)
                {
                    throw new InvalidDataException("The signed reference does not match the XML reference");
                }
            }
        }

        /// <summary>
        /// Verifies the digital signature of an enveloped XML using the provided certificate
        /// </summary>
        /// <param name="signedXmlBytes">The signed XML as byte array</param>
        /// <param name="certificate">Certificate to verify the signature</param>
        /// <returns>True if signature is valid, false otherwise</returns>
        public static bool VerifyEnvelopedXmlSignature(byte[] signedXmlBytes, X509Certificate2 certificate)
        {
            try
            {
                var publicKeyBytes = certificate.Export(X509ContentType.Cert);
                return CheckSignature(signedXmlBytes, publicKeyBytes);
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Removes the digital signature from a signed XML and returns the original content
        /// </summary>
        /// <param name="signedXmlBytes">The signed XML as byte array</param>
        /// <returns>Original XML content without signature as byte array</returns>
        public static byte[] RemoveSignatureFromXml(byte[] signedXmlBytes)
        {
            try
            {
                var xmlDoc = XmlHelper.LoadToXmlDocument(signedXmlBytes);

                // Find and remove the Signature element
                var signatureNodes = xmlDoc.GetElementsByTagName("Signature", "http://www.w3.org/2000/09/xmldsig#");
                if (signatureNodes.Count > 0)
                {
                    // For enveloped signatures, we need to extract the original content from the Object element
                    var objectNodes = xmlDoc.GetElementsByTagName("Object", "http://www.w3.org/2000/09/xmldsig#");
                    if (objectNodes.Count > 0 && objectNodes[0] != null)
                    {
                        var objectElement = objectNodes[0] as XmlElement;
                        if (objectElement?.FirstChild != null)
                        {
                            // The original XML is inside the Object element
                            var originalContent = objectElement.FirstChild.OuterXml;
                            return Encoding.UTF8.GetBytes(originalContent);
                        }
                    }
                }

                // If no signature found or extraction failed, return the original content
                // Remove any signature elements that might exist
                while (signatureNodes.Count > 0)
                {
                    signatureNodes[0]?.ParentNode?.RemoveChild(signatureNodes[0]);
                    signatureNodes = xmlDoc.GetElementsByTagName("Signature", "http://www.w3.org/2000/09/xmldsig#");
                }

                return Encoding.UTF8.GetBytes($"<?xml version=\"1.0\" encoding=\"utf-8\"?>{xmlDoc.OuterXml}");
            }
            catch (Exception)
            {
                // If extraction fails, return the original content
                return signedXmlBytes;
            }
        }
    }
}
using Bdo.Ess.CtsIntegration.CtsPackageGeneration;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using Xunit;
using Shouldly;
using Volo.Abp.Modularity;

namespace Bdo.Ess.CtsIntegration.CtsPackageGeneration
{
    public class ZipManager_Tests : CtsIntegrationApplicationTestBase<CtsIntegrationApplicationTestModule>
    {
        private readonly byte[] _testFileContent = Encoding.UTF8.GetBytes("This is test file content for ZIP operations.");
        private readonly string _testFileName = "test.txt";
        private readonly byte[] _secondTestFileContent = Encoding.UTF8.GetBytes("Second test file with different content!");
        private readonly string _secondTestFileName = "second.txt";

        [Fact]
        public void CreateZipFromMemory_Should_Create_ValidZip()
        {
            // Act
            var zipBytes = ZipManager.CreateZipFromMemory(_testFileName, _testFileContent);

            // Assert
            zipBytes.ShouldNotBeNull();
            zipBytes.Length.ShouldBeGreaterThan(0);
            
            // Verify the ZIP is valid by trying to read it
            using var stream = new MemoryStream(zipBytes);
            using var archive = new ZipArchive(stream, ZipArchiveMode.Read);
            archive.Entries.Count.ShouldBe(1);
            archive.Entries.First().Name.ShouldBe(_testFileName);
        }

        [Fact]
        public void CreateZipFromMemory_Should_Preserve_FileContent()
        {
            // Act
            var zipBytes = ZipManager.CreateZipFromMemory(_testFileName, _testFileContent);

            // Assert - Extract and verify content
            using var stream = new MemoryStream(zipBytes);
            using var archive = new ZipArchive(stream, ZipArchiveMode.Read);
            
            var entry = archive.Entries.First();
            using var entryStream = entry.Open();
            using var reader = new StreamReader(entryStream);
            var content = reader.ReadToEnd();
            
            content.ShouldBe(Encoding.UTF8.GetString(_testFileContent));
        }

        [Fact]
        public void CreateZipFromMemory_Should_Handle_EmptyContent()
        {
            // Arrange
            var emptyContent = new byte[0];
            var fileName = "empty.txt";

            // Act
            var zipBytes = ZipManager.CreateZipFromMemory(fileName, emptyContent);

            // Assert
            zipBytes.ShouldNotBeNull();
            zipBytes.Length.ShouldBeGreaterThan(0);

            using var stream = new MemoryStream(zipBytes);
            using var archive = new ZipArchive(stream, ZipArchiveMode.Read);
            archive.Entries.Count.ShouldBe(1);
            archive.Entries.First().Name.ShouldBe(fileName);
            archive.Entries.First().Length.ShouldBe(0);
        }

        [Fact]
        public void CreateZipFromMemory_Should_Handle_LargeContent()
        {
            // Arrange
            var largeContent = new byte[1024 * 1024]; // 1MB
            for (int i = 0; i < largeContent.Length; i++)
            {
                largeContent[i] = (byte)(i % 256);
            }
            var fileName = "large.bin";

            // Act
            var zipBytes = ZipManager.CreateZipFromMemory(fileName, largeContent);

            // Assert
            zipBytes.ShouldNotBeNull();
            zipBytes.Length.ShouldBeGreaterThan(0);
            zipBytes.Length.ShouldBeLessThan(largeContent.Length); // Should be compressed

            using var stream = new MemoryStream(zipBytes);
            using var archive = new ZipArchive(stream, ZipArchiveMode.Read);
            archive.Entries.Count.ShouldBe(1);
            archive.Entries.First().Name.ShouldBe(fileName);
        }

        [Fact]
        public void CreateZipFromMemory_Should_Handle_SpecialCharacters_In_FileName()
        {
            // Arrange
            var fileName = "test file with spaces & symbols.txt";

            // Act
            var zipBytes = ZipManager.CreateZipFromMemory(fileName, _testFileContent);

            // Assert
            zipBytes.ShouldNotBeNull();

            using var stream = new MemoryStream(zipBytes);
            using var archive = new ZipArchive(stream, ZipArchiveMode.Read);
            archive.Entries.Count.ShouldBe(1);
            archive.Entries.First().FullName.ShouldBe(fileName);
        }

        [Theory]
        [InlineData("")]
        public void CreateZipFromMemory_Should_Handle_Invalid_FileName(string fileName)
        {
            // Act & Assert
            var exception = Should.Throw<Exception>(() => 
                ZipManager.CreateZipFromMemory(fileName, _testFileContent));
            
            exception.ShouldNotBeNull();
        }

        [Fact]
        public void AddFileToExistingZip_Should_Add_File_To_ExistingZip()
        {
            // Arrange
            var originalZip = ZipManager.CreateZipFromMemory(_testFileName, _testFileContent);

            // Act
            var updatedZip = ZipManager.AddFileToExistingZip(originalZip, _secondTestFileName, _secondTestFileContent);

            // Assert
            updatedZip.ShouldNotBeNull();
            updatedZip.Length.ShouldBeGreaterThan(originalZip.Length);

            using var stream = new MemoryStream(updatedZip);
            using var archive = new ZipArchive(stream, ZipArchiveMode.Read);
            archive.Entries.Count.ShouldBe(2);
            
            var fileNames = archive.Entries.Select(e => e.Name).ToArray();
            fileNames.ShouldContain(_testFileName);
            fileNames.ShouldContain(_secondTestFileName);
        }

        [Fact]
        public void AddFileToExistingZip_Should_Preserve_Original_Content()
        {
            // Arrange
            var originalZip = ZipManager.CreateZipFromMemory(_testFileName, _testFileContent);

            // Act
            var updatedZip = ZipManager.AddFileToExistingZip(originalZip, _secondTestFileName, _secondTestFileContent);

            // Assert
            using var stream = new MemoryStream(updatedZip);
            using var archive = new ZipArchive(stream, ZipArchiveMode.Read);
            
            var originalEntry = archive.Entries.First(e => e.Name == _testFileName);
            using var entryStream = originalEntry.Open();
            using var reader = new StreamReader(entryStream);
            var content = reader.ReadToEnd();
            
            content.ShouldBe(Encoding.UTF8.GetString(_testFileContent));
        }

        [Fact]
        public void AddFileToExistingZip_Should_Add_New_Content_Correctly()
        {
            // Arrange
            var originalZip = ZipManager.CreateZipFromMemory(_testFileName, _testFileContent);

            // Act
            var updatedZip = ZipManager.AddFileToExistingZip(originalZip, _secondTestFileName, _secondTestFileContent);

            // Assert
            using var stream = new MemoryStream(updatedZip);
            using var archive = new ZipArchive(stream, ZipArchiveMode.Read);
            
            var newEntry = archive.Entries.First(e => e.Name == _secondTestFileName);
            using var entryStream = newEntry.Open();
            using var reader = new StreamReader(entryStream);
            var content = reader.ReadToEnd();
            
            content.ShouldBe(Encoding.UTF8.GetString(_secondTestFileContent));
        }

        [Fact]
        public void AddFileToExistingZip_Should_Handle_Multiple_Additions()
        {
            // Arrange
            var originalZip = ZipManager.CreateZipFromMemory(_testFileName, _testFileContent);
            var thirdFileName = "third.txt";
            var thirdFileContent = Encoding.UTF8.GetBytes("Third file content");

            // Act
            var zipWithSecondFile = ZipManager.AddFileToExistingZip(originalZip, _secondTestFileName, _secondTestFileContent);
            var zipWithThirdFile = ZipManager.AddFileToExistingZip(zipWithSecondFile, thirdFileName, thirdFileContent);

            // Assert
            using var stream = new MemoryStream(zipWithThirdFile);
            using var archive = new ZipArchive(stream, ZipArchiveMode.Read);
            archive.Entries.Count.ShouldBe(3);
            
            var fileNames = archive.Entries.Select(e => e.Name).ToArray();
            fileNames.ShouldContain(_testFileName);
            fileNames.ShouldContain(_secondTestFileName);
            fileNames.ShouldContain(thirdFileName);
        }

        [Fact]
        public void AddFileToExistingZip_Should_Throw_Exception_When_ZipBytes_Invalid()
        {
            // Arrange
            var invalidZipBytes = Encoding.UTF8.GetBytes("This is not a valid ZIP file");

            // Act & Assert
            Should.Throw<InvalidDataException>(() => 
                ZipManager.AddFileToExistingZip(invalidZipBytes, _testFileName, _testFileContent));
        }

        [Fact]
        public void AddFileToExistingZip_Should_Handle_Empty_ZipBytes()
        {
            // Arrange
            var emptyBytes = new byte[0];

            // Act
            var result = Record.Exception(() => 
                ZipManager.AddFileToExistingZip(emptyBytes, _testFileName, _testFileContent));

            // Assert - The method should either throw an exception or handle empty bytes gracefully
            if (result != null)
            {
                // If it throws an exception, it should be one of these expected types
                bool isExpectedException = result is ArgumentException || 
                                         result is InvalidDataException || 
                                         result is EndOfStreamException;
                isExpectedException.ShouldBeTrue();
            }
            else
            {
                // If it doesn't throw, verify we can still get a valid result
                var zipResult = ZipManager.AddFileToExistingZip(emptyBytes, _testFileName, _testFileContent);
                zipResult.ShouldNotBeNull();
                zipResult.Length.ShouldBeGreaterThan(0);
            }
        }

        [Fact]
        public void ExtractFilesFromZip_Should_Extract_Single_File()
        {
            // Arrange
            var zipBytes = ZipManager.CreateZipFromMemory(_testFileName, _testFileContent);
            using var stream = new MemoryStream(zipBytes);

            // Act
            var extractedFiles = ZipManager.ExtractFilesFromZip(stream);

            // Assert
            extractedFiles.ShouldNotBeNull();
            extractedFiles.Count.ShouldBe(1);
            extractedFiles.ShouldContainKey(_testFileName);
            extractedFiles[_testFileName].ShouldBe(_testFileContent);
        }

        [Fact]
        public void ExtractFilesFromZip_Should_Extract_Multiple_Files()
        {
            // Arrange
            var zipWithOneFile = ZipManager.CreateZipFromMemory(_testFileName, _testFileContent);
            var zipWithTwoFiles = ZipManager.AddFileToExistingZip(zipWithOneFile, _secondTestFileName, _secondTestFileContent);
            using var stream = new MemoryStream(zipWithTwoFiles);

            // Act
            var extractedFiles = ZipManager.ExtractFilesFromZip(stream);

            // Assert
            extractedFiles.ShouldNotBeNull();
            extractedFiles.Count.ShouldBe(2);
            extractedFiles.ShouldContainKey(_testFileName);
            extractedFiles.ShouldContainKey(_secondTestFileName);
            extractedFiles[_testFileName].ShouldBe(_testFileContent);
            extractedFiles[_secondTestFileName].ShouldBe(_secondTestFileContent);
        }

        [Fact]
        public void ExtractFilesFromZip_Should_Handle_Empty_Zip()
        {
            // Arrange - Create empty ZIP
            using var zipStream = new MemoryStream();
            using (var archive = new ZipArchive(zipStream, ZipArchiveMode.Create, leaveOpen: true))
            {
                // Create empty archive
            }
            zipStream.Position = 0;

            // Act
            var extractedFiles = ZipManager.ExtractFilesFromZip(zipStream);

            // Assert
            extractedFiles.ShouldNotBeNull();
            extractedFiles.Count.ShouldBe(0);
        }

        [Fact]
        public void ExtractFilesFromZip_Should_Handle_Files_With_Subdirectories()
        {
            // Arrange
            var fileInSubdir = "subfolder/nested.txt";
            var fileContent = Encoding.UTF8.GetBytes("Nested file content");
            
            using var zipStream = new MemoryStream();
            using (var archive = new ZipArchive(zipStream, ZipArchiveMode.Create, leaveOpen: true))
            {
                var entry = archive.CreateEntry(fileInSubdir);
                using var entryStream = entry.Open();
                entryStream.Write(fileContent, 0, fileContent.Length);
            }
            zipStream.Position = 0;

            // Act
            var extractedFiles = ZipManager.ExtractFilesFromZip(zipStream);

            // Assert
            extractedFiles.ShouldNotBeNull();
            extractedFiles.Count.ShouldBe(1);
            extractedFiles.ShouldContainKey(fileInSubdir);
            extractedFiles[fileInSubdir].ShouldBe(fileContent);
        }

        [Fact]
        public void ExtractFilesFromZip_Should_Throw_Exception_When_Stream_Invalid()
        {
            // Arrange
            var invalidStream = new MemoryStream(Encoding.UTF8.GetBytes("This is not a valid ZIP"));

            // Act & Assert
            Should.Throw<InvalidDataException>(() => 
                ZipManager.ExtractFilesFromZip(invalidStream));
        }

        [Fact]
        public void ExtractFilesFromZip_Should_Handle_Binary_Files()
        {
            // Arrange
            var binaryContent = new byte[] { 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A }; // PNG header
            var binaryFileName = "image.png";
            var zipBytes = ZipManager.CreateZipFromMemory(binaryFileName, binaryContent);
            using var stream = new MemoryStream(zipBytes);

            // Act
            var extractedFiles = ZipManager.ExtractFilesFromZip(stream);

            // Assert
            extractedFiles.ShouldNotBeNull();
            extractedFiles.Count.ShouldBe(1);
            extractedFiles.ShouldContainKey(binaryFileName);
            extractedFiles[binaryFileName].ShouldBe(binaryContent);
        }

        [Fact]
        public void Complete_ZipWorkflow_Should_Work_EndToEnd()
        {
            // Arrange
            var file1Name = "document1.txt";
            var file1Content = Encoding.UTF8.GetBytes("First document content");
            var file2Name = "document2.txt";
            var file2Content = Encoding.UTF8.GetBytes("Second document content");
            var file3Name = "data/config.json";
            var file3Content = Encoding.UTF8.GetBytes("{\"setting\": \"value\"}");

            // Act - Step 1: Create initial ZIP
            var initialZip = ZipManager.CreateZipFromMemory(file1Name, file1Content);

            // Act - Step 2: Add second file
            var zipWithTwoFiles = ZipManager.AddFileToExistingZip(initialZip, file2Name, file2Content);

            // Act - Step 3: Add third file (in subdirectory)
            var finalZip = ZipManager.AddFileToExistingZip(zipWithTwoFiles, file3Name, file3Content);

            // Act - Step 4: Extract all files
            using var stream = new MemoryStream(finalZip);
            var extractedFiles = ZipManager.ExtractFilesFromZip(stream);

            // Assert
            extractedFiles.Count.ShouldBe(3);
            extractedFiles.ShouldContainKey(file1Name);
            extractedFiles.ShouldContainKey(file2Name);
            extractedFiles.ShouldContainKey(file3Name);
            
            extractedFiles[file1Name].ShouldBe(file1Content);
            extractedFiles[file2Name].ShouldBe(file2Content);
            extractedFiles[file3Name].ShouldBe(file3Content);
        }

        [Fact]
        public void ZipOperations_Should_Preserve_File_Order()
        {
            // Arrange
            var files = new Dictionary<string, byte[]>
            {
                ["a_first.txt"] = Encoding.UTF8.GetBytes("First"),
                ["b_second.txt"] = Encoding.UTF8.GetBytes("Second"),
                ["c_third.txt"] = Encoding.UTF8.GetBytes("Third")
            };

            // Act - Create ZIP with multiple files
            var zipBytes = ZipManager.CreateZipFromMemory(files.First().Key, files.First().Value);
            foreach (var file in files.Skip(1))
            {
                zipBytes = ZipManager.AddFileToExistingZip(zipBytes, file.Key, file.Value);
            }

            // Extract and verify
            using var stream = new MemoryStream(zipBytes);
            var extractedFiles = ZipManager.ExtractFilesFromZip(stream);

            // Assert
            extractedFiles.Count.ShouldBe(files.Count);
            foreach (var file in files)
            {
                extractedFiles.ShouldContainKey(file.Key);
                extractedFiles[file.Key].ShouldBe(file.Value);
            }
        }

        [Fact]
        public void CreateZipFromMemory_Should_Use_Optimal_Compression()
        {
            // Arrange - Create highly compressible content (repeated text)
            var repetitiveContent = Encoding.UTF8.GetBytes(new string('A', 1000));

            // Act
            var zipBytes = ZipManager.CreateZipFromMemory("repetitive.txt", repetitiveContent);

            // Assert
            zipBytes.Length.ShouldBeLessThan(repetitiveContent.Length / 2); // Should compress to less than 50%

            // Verify compression level by checking the ZIP entry
            using var stream = new MemoryStream(zipBytes);
            using var archive = new ZipArchive(stream, ZipArchiveMode.Read);
            var entry = archive.Entries.First();
            entry.CompressedLength.ShouldBeLessThan(entry.Length); // Compressed size should be less than original
        }
    }
}

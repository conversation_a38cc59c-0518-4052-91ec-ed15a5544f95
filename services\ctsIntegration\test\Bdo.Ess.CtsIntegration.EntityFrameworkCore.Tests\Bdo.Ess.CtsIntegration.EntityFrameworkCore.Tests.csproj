<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Bdo.Ess.CtsIntegration</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1.0" />
    <PackageReference Include="NSubstitute" Version="5.1.0" />
    <PackageReference Include="Shouldly" Version="4.2.1" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.Sqlite" Version="8.2.3" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.extensibility.execution" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
    <ProjectReference Include="..\Bdo.Ess.CtsIntegration.Application.Tests\Bdo.Ess.CtsIntegration.Application.Tests.csproj" />
    <ProjectReference Include="..\..\src\Bdo.Ess.CtsIntegration.EntityFrameworkCore\Bdo.Ess.CtsIntegration.EntityFrameworkCore.csproj" />
  </ItemGroup>

</Project>

using Bdo.Ess.CtsIntegration.BahamasCtsSettings;
using Bdo.Ess.CtsIntegration.Encryption;
using Bdo.Ess.CtsIntegration.Entities;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.Audit;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using System;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Xunit;

namespace Bdo.Ess.CtsIntegration.EntityFrameworkCore.Applications;

/// <summary>
/// Unit tests for BahamasCtsSettingAppService following ABP test patterns.
/// Tests cover CRUD operations, encryption/decryption, audit logging, and business rules.
/// Uses SQLite in-memory database for EF Core integration testing.
/// </summary>
public class BahamasCtsSettingAppService_Tests : CtsIntegrationEntityFrameworkCoreTestBase
{
    private readonly IBahamasCtsSettingAppService _appService;
    private readonly IRepository<BahamasCtsSetting, Guid> _repository;
    private readonly ICtsEncryptionManager _mockEncryptionManager;
    private readonly IDistributedEventBus _mockEventBus;
    private readonly IAuditWebInfo _mockAuditWebInfo;
    private readonly ILogger<BahamasCtsSettingAppService> _mockLogger;

    public BahamasCtsSettingAppService_Tests()
    {
        // Get real repository from DI container for EF Core integration testing
        _repository = GetRequiredService<IRepository<BahamasCtsSetting, Guid>>();
        
        // Create mocks for external dependencies
        _mockEncryptionManager = Substitute.For<ICtsEncryptionManager>();
        _mockEventBus = Substitute.For<IDistributedEventBus>();
        _mockAuditWebInfo = Substitute.For<IAuditWebInfo>();
        _mockLogger = Substitute.For<ILogger<BahamasCtsSettingAppService>>();

        // Setup mock behaviors
        SetupMockEncryptionManager();
        SetupMockAuditWebInfo();

        // Create the app service with mocked dependencies
        _appService = new BahamasCtsSettingAppService(
            _repository,
            _mockEncryptionManager,
            _mockEventBus,
            _mockAuditWebInfo,
            _mockLogger);
    }

    #region Create Tests

    [Fact]
    public async Task CreateAsync_Should_Create_BahamasCtsSetting_When_ValidInput()
    {
        // Arrange
        var sshKey = "test-ssh-key-content";
        var input = CreateValidCreateDto();

        // Act
        var result = await _appService.CreateAsync(sshKey, input);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBe(Guid.Empty);
        result.SystemUserName.ShouldBe(input.SystemUserName);
        result.SystemUserPassword.ShouldBe(input.SystemUserPassword);
        result.SftpUserName.ShouldBe(input.SftpUserName);
        result.SftpSSHKey.ShouldBe(sshKey);
        result.SystemUserPasswordUpdatedAt.ShouldNotBeNull();
        result.SftpSSHKeyUpdatedAt.ShouldNotBeNull();

        // Verify encryption was called
        _mockEncryptionManager.Received(1).EncryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>());
        _mockEncryptionManager.Received(1).DecryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>(), true);

        // Verify audit event was published
        await _mockEventBus.Received(1).PublishAsync(Arg.Any<AuditCtsSettingEto>());
    }

    [Fact]
    public async Task CreateAsync_Should_Throw_Exception_When_SettingAlreadyExists()
    {
        // Arrange
        await SeedTestDataAsync();
        var sshKey = "test-ssh-key-content";
        var input = CreateValidCreateDto();

        // Act & Assert
        var exception = await Should.ThrowAsync<UserFriendlyException>(
            () => _appService.CreateAsync(sshKey, input));
        
        exception.Message.ShouldContain("A Bahamas CTS setting already exists");
    }

    [Fact]
    public async Task CreateAsync_Should_Set_Timestamps_Correctly()
    {
        // Arrange
        var beforeCreate = DateTime.UtcNow.AddSeconds(-1);
        var sshKey = "test-ssh-key-content";
        var input = CreateValidCreateDto();

        // Act
        var result = await _appService.CreateAsync(sshKey, input);
        var afterCreate = DateTime.UtcNow.AddSeconds(1);

        // Assert
        result.SystemUserPasswordUpdatedAt.ShouldNotBeNull();
        result.SystemUserPasswordUpdatedAt.Value.ShouldBeGreaterThan(beforeCreate);
        result.SystemUserPasswordUpdatedAt.Value.ShouldBeLessThan(afterCreate);
        
        result.SftpSSHKeyUpdatedAt.ShouldNotBeNull();
        result.SftpSSHKeyUpdatedAt.Value.ShouldBeGreaterThan(beforeCreate);
        result.SftpSSHKeyUpdatedAt.Value.ShouldBeLessThan(afterCreate);
    }

    #endregion

    #region Update Tests

    [Fact]
    public async Task UpdateAsync_Should_Update_BahamasCtsSetting_When_ValidInput()
    {
        // Arrange
        var existingEntity = await SeedTestDataAsync();
        var sshKey = "updated-ssh-key-content";
        var input = CreateValidUpdateDto(existingEntity.Id);
        input.SystemUserName = "UpdatedUserName";
        input.SystemUserPassword = "UpdatedPassword";
        input.SftpUserName = "UpdatedSftpUser";

        // Act
        var result = await _appService.UpdateAsync(sshKey, input);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingEntity.Id);
        result.SystemUserName.ShouldBe(input.SystemUserName);
        result.SystemUserPassword.ShouldBe(input.SystemUserPassword);
        result.SftpUserName.ShouldBe(input.SftpUserName);
        result.SftpSSHKey.ShouldBe(sshKey);

        // Verify encryption was called
        _mockEncryptionManager.Received(1).DecryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>());
        _mockEncryptionManager.Received(1).EncryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>());
        _mockEncryptionManager.Received(1).DecryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>(), true);

        // Verify audit event was published
        await _mockEventBus.Received(1).PublishAsync(Arg.Any<AuditCtsSettingEto>());
    }

    [Fact]
    public async Task UpdateAsync_Should_Throw_Exception_When_EntityNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();
        var sshKey = "test-ssh-key-content";
        var input = CreateValidUpdateDto(nonExistentId);

        // Act & Assert
        var exception = await Should.ThrowAsync<UserFriendlyException>(
            () => _appService.UpdateAsync(sshKey, input));
        
        exception.Message.ShouldContain("Bahamas CTS setting not found");
    }

    #endregion

    #region GetCurrentSettings Tests

    [Fact]
    public async Task GetCurrentSettingsAsync_Should_Return_LatestSetting_When_SettingsExist()
    {
        // Arrange
        var firstEntity = await SeedTestDataAsync();
        await Task.Delay(100); // Ensure different creation times
        var secondEntity = await SeedTestDataAsync("SecondUser", "SecondPassword", "SecondSftpUser");

        // Act
        var result = await _appService.GetCurrentSettingsAsync();

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(secondEntity.Id); // Should return the latest one
        result.SystemUserName.ShouldBe("SecondUser");

        // Verify decryption was called
        _mockEncryptionManager.Received().DecryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>());
    }

    [Fact]
    public async Task GetCurrentSettingsAsync_Should_Return_Null_When_NoSettingsExist()
    {
        // Act
        var result = await _appService.GetCurrentSettingsAsync();

        // Assert
        result.ShouldBeNull();
    }

    #endregion

    #region Update Timestamp Tests

    [Fact]
    public async Task UpdateAsync_Should_Update_SystemUserPasswordUpdatedAt_When_PasswordChanged()
    {
        // Arrange
        var existingEntity = await SeedTestDataAsync();
        var originalPasswordUpdatedAt = existingEntity.SystemUserPasswordUpdatedAt;
        await Task.Delay(100); // Ensure different timestamps

        var sshKey = "updated-ssh-key-content";
        var input = CreateValidUpdateDto(existingEntity.Id);
        input.SystemUserPassword = "NewPassword123!";

        // Act
        var result = await _appService.UpdateAsync(sshKey, input);

        // Assert
        result.SystemUserPasswordUpdatedAt.ShouldNotBeNull();
        result.SystemUserPasswordUpdatedAt.Value.ShouldBeGreaterThan(originalPasswordUpdatedAt!.Value);
    }

    [Fact]
    public async Task UpdateAsync_Should_Update_SftpSSHKeyUpdatedAt_When_SshKeyChanged()
    {
        // Arrange
        var existingEntity = await SeedTestDataAsync();
        var originalSshKeyUpdatedAt = existingEntity.SftpSSHKeyUpdatedAt;
        await Task.Delay(100); // Ensure different timestamps

        var newSshKey = "completely-new-ssh-key-content";
        var input = CreateValidUpdateDto(existingEntity.Id);

        // Act
        var result = await _appService.UpdateAsync(newSshKey, input);

        // Assert
        result.SftpSSHKeyUpdatedAt.ShouldNotBeNull();
        result.SftpSSHKeyUpdatedAt.Value.ShouldBeGreaterThan(originalSshKeyUpdatedAt!.Value);
    }

    [Fact]
    public async Task UpdateAsync_Should_Not_Update_Timestamps_When_NoChanges()
    {
        // Arrange
        var existingEntity = await SeedTestDataAsync();
        var originalPasswordUpdatedAt = existingEntity.SystemUserPasswordUpdatedAt;
        var originalSshKeyUpdatedAt = existingEntity.SftpSSHKeyUpdatedAt;

        var input = CreateValidUpdateDto(existingEntity.Id);
        input.SystemUserName = existingEntity.SystemUserName;
        input.SystemUserPassword = existingEntity.SystemUserPassword;
        input.SftpUserName = existingEntity.SftpUserName;

        // Act
        var result = await _appService.UpdateAsync(existingEntity.SftpSSHKey, input);

        // Assert
        result.SystemUserPasswordUpdatedAt.ShouldBe(originalPasswordUpdatedAt);
        result.SftpSSHKeyUpdatedAt.ShouldBe(originalSshKeyUpdatedAt);
    }

    #endregion

    #region Encryption Integration Tests

    [Fact]
    public async Task CreateAsync_Should_Handle_Encryption_Correctly()
    {
        // Arrange
        var sshKey = "test-ssh-key-content";
        var input = CreateValidCreateDto();

        // Setup encryption manager to simulate actual encryption/decryption
        _mockEncryptionManager.EncryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>())
            .Returns(callInfo =>
            {
                var entity = callInfo.Arg<BahamasCtsSetting>();
                entity.SystemUserPassword = $"ENCRYPTED_{entity.SystemUserPassword}";
                entity.SftpSSHKey = $"ENCRYPTED_{entity.SftpSSHKey}";
                return entity;
            });

        _mockEncryptionManager.DecryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>(), true)
            .Returns(callInfo =>
            {
                var entity = callInfo.Arg<BahamasCtsSetting>();
                return new BahamasCtsSetting(entity.Id)
                {
                    SystemUserName = entity.SystemUserName,
                    SystemUserPassword = entity.SystemUserPassword.Replace("ENCRYPTED_", ""),
                    SftpUserName = entity.SftpUserName,
                    SftpSSHKey = entity.SftpSSHKey.Replace("ENCRYPTED_", ""),
                    SystemUserPasswordUpdatedAt = entity.SystemUserPasswordUpdatedAt,
                    SftpSSHKeyUpdatedAt = entity.SftpSSHKeyUpdatedAt
                };
            });

        // Act
        var result = await _appService.CreateAsync(sshKey, input);

        // Assert
        result.SystemUserPassword.ShouldBe(input.SystemUserPassword); // Should be decrypted in result
        result.SftpSSHKey.ShouldBe(sshKey); // Should be decrypted in result

        // Verify the entity in database is encrypted
        var entityInDb = await _repository.GetAsync(result.Id);
        entityInDb.SystemUserPassword.ShouldBe($"ENCRYPTED_{input.SystemUserPassword}");
        entityInDb.SftpSSHKey.ShouldBe($"ENCRYPTED_{sshKey}");
    }

    [Fact]
    public async Task GetCurrentSettingsAsync_Should_Decrypt_SensitiveData()
    {
        // Arrange
        var entity = await SeedTestDataAsync();

        // Setup decryption to simulate actual behavior
        _mockEncryptionManager.DecryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>())
            .Returns(callInfo =>
            {
                var setting = callInfo.Arg<BahamasCtsSetting>();
                setting.SystemUserPassword = "DECRYPTED_PASSWORD";
                setting.SftpSSHKey = "DECRYPTED_SSH_KEY";
                return setting;
            });

        // Act
        var result = await _appService.GetCurrentSettingsAsync();

        // Assert
        result.ShouldNotBeNull();
        result.SystemUserPassword.ShouldBe("DECRYPTED_PASSWORD");
        result.SftpSSHKey.ShouldBe("DECRYPTED_SSH_KEY");

        // Verify decryption was called
        _mockEncryptionManager.Received(1).DecryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>());
    }

    #endregion

    #region Audit Logging Tests

    [Fact]
    public async Task CreateAsync_Should_Publish_AuditEvent_With_CorrectData()
    {
        // Arrange
        var sshKey = "test-ssh-key-content";
        var input = CreateValidCreateDto();

        // Act
        await _appService.CreateAsync(sshKey, input);

        // Assert
        await _mockEventBus.Received(1).PublishAsync(
            Arg.Is<AuditCtsSettingEto>(eto =>
                eto.Action == AuditActionEnum.CtsSetting &&
                eto.NewValue != null));
    }

    [Fact]
    public async Task UpdateAsync_Should_Publish_AuditEvent_With_PasswordChange()
    {
        // Arrange
        var existingEntity = await SeedTestDataAsync();
        var sshKey = "updated-ssh-key-content";
        var input = CreateValidUpdateDto(existingEntity.Id);
        input.SystemUserPassword = "NewPassword123!";

        // Act
        await _appService.UpdateAsync(sshKey, input);

        // Assert
        await _mockEventBus.Received(1).PublishAsync(
            Arg.Is<AuditCtsSettingEto>(eto =>
                eto.Action == AuditActionEnum.CtsSetting &&
                eto.NewValue != null));
    }

    [Fact]
    public async Task UpdateAsync_Should_Publish_AuditEvent_With_NoPasswordChange()
    {
        // Arrange
        var existingEntity = await SeedTestDataAsync();
        var input = CreateValidUpdateDto(existingEntity.Id);
        input.SystemUserPassword = existingEntity.SystemUserPassword; // Same password

        // Act
        await _appService.UpdateAsync(existingEntity.SftpSSHKey, input); // Same SSH key

        // Assert
        await _mockEventBus.Received(1).PublishAsync(
            Arg.Is<AuditCtsSettingEto>(eto =>
                eto.Action == AuditActionEnum.CtsSetting &&
                eto.NewValue != null));
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task CreateAsync_Should_Handle_EmptySSHKey()
    {
        // Arrange
        var sshKey = "";
        var input = CreateValidCreateDto();

        // Act
        var result = await _appService.CreateAsync(sshKey, input);

        // Assert
        result.ShouldNotBeNull();
        result.SftpSSHKey.ShouldBe("");
    }

    [Fact]
    public async Task UpdateAsync_Should_Handle_NullSSHKey()
    {
        // Arrange
        var existingEntity = await SeedTestDataAsync();
        string? sshKey = null;
        var input = CreateValidUpdateDto(existingEntity.Id);

        // Act
        var result = await _appService.UpdateAsync(sshKey!, input);

        // Assert
        result.ShouldNotBeNull();
        result.SftpSSHKey.ShouldBeNull();
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Seeds test data into the database
    /// </summary>
    private async Task<BahamasCtsSetting> SeedTestDataAsync(
        string systemUserName = "TestUser",
        string systemUserPassword = "TestPassword",
        string sftpUserName = "TestSftpUser")
    {
        var entity = new BahamasCtsSetting(Guid.NewGuid())
        {
            SystemUserName = systemUserName,
            SystemUserPassword = systemUserPassword,
            SftpUserName = sftpUserName,
            SftpSSHKey = "test-ssh-key",
            SystemUserPasswordUpdatedAt = DateTime.UtcNow,
            SftpSSHKeyUpdatedAt = DateTime.UtcNow
        };

        return await _repository.InsertAsync(entity, autoSave: true);
    }

    /// <summary>
    /// Creates a valid CreateBahamasCtsSettingDto for testing
    /// </summary>
    private CreateBahamasCtsSettingDto CreateValidCreateDto()
    {
        return new CreateBahamasCtsSettingDto
        {
            SystemUserName = "TestUser",
            SystemUserPassword = "TestPassword123!",
            SftpUserName = "TestSftpUser"
        };
    }

    /// <summary>
    /// Creates a valid UpdateBahamasCtsSettingDto for testing
    /// </summary>
    private UpdateBahamasCtsSettingDto CreateValidUpdateDto(Guid id)
    {
        return new UpdateBahamasCtsSettingDto
        {
            Id = id,
            SystemUserName = "UpdatedUser",
            SystemUserPassword = "UpdatedPassword123!",
            SftpUserName = "UpdatedSftpUser"
        };
    }

    /// <summary>
    /// Sets up mock behaviors for the encryption manager
    /// </summary>
    private void SetupMockEncryptionManager()
    {
        // Setup EncryptBahamasCtsSetting to return the same entity (simulating encryption)
        _mockEncryptionManager.EncryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>())
            .Returns(callInfo => callInfo.Arg<BahamasCtsSetting>());

        // Setup DecryptBahamasCtsSetting to return the same entity or a new instance
        _mockEncryptionManager.DecryptBahamasCtsSetting(Arg.Any<BahamasCtsSetting>(), Arg.Any<bool>())
            .Returns(callInfo =>
            {
                var entity = callInfo.Arg<BahamasCtsSetting>();
                var newInstance = callInfo.Arg<bool>();
                
                if (newInstance)
                {
                    return new BahamasCtsSetting(entity.Id)
                    {
                        SystemUserName = entity.SystemUserName,
                        SystemUserPassword = entity.SystemUserPassword,
                        SftpUserName = entity.SftpUserName,
                        SftpSSHKey = entity.SftpSSHKey,
                        SystemUserPasswordUpdatedAt = entity.SystemUserPasswordUpdatedAt,
                        SftpSSHKeyUpdatedAt = entity.SftpSSHKeyUpdatedAt
                    };
                }
                return entity;
            });
    }

    /// <summary>
    /// Sets up mock behaviors for audit web info
    /// </summary>
    private void SetupMockAuditWebInfo()
    {
        _mockAuditWebInfo.IPAddress.Returns("127.0.0.1");
        _mockAuditWebInfo.AuditUserId.Returns("test-user-id");
    }

    #endregion
}

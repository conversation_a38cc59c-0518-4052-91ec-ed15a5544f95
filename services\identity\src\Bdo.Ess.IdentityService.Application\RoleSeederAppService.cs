using Bdo.Ess.AuditService.Permissions;
using Bdo.Ess.CtsIntegration.Permissions;
using Bdo.Ess.EconomicSubstanceService.Permissions;
using Bdo.Ess.IdentityService.Helpers;
using Bdo.Ess.Shared.Constants.Roles;
using Bdo.Ess.Shared.Hosting.Email;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.Tenants;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NUglify.Helpers;
using PasswordGenerator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.Users;
using static Bdo.Ess.AuditService.Permissions.AuditServicePermissions;
using static Bdo.Ess.DashboardService.Permissions.DashboardServicePermissions;

namespace Bdo.Ess.IdentityService
{
    public class RoleSeederAppService : ApplicationService, IRoleSeederAppService
                    , IDistributedEventHandler<NewRATenantCreatedEto>,
                    IDistributedEventHandler<NewCATenantCreatedEto>
    {
        private readonly IGuidGenerator _guidGenerator;
        private readonly IIdentityRoleRepository _roleRepository;
        private readonly IRepository<PermissionGrant, Guid> _permissionrepo;
        private readonly IdentityUserManager _identityUserManager;
        private readonly BdoEmailService _emailHelper;
        private readonly TenantUrlProvider _tenantUrlProvider;
        private readonly IConfiguration _configuration;

        public RoleSeederAppService(IGuidGenerator guidGenerator,
            IIdentityRoleRepository roleRepository,
            IdentityUserManager identityUserManager,
            BdoEmailService emailHelper,
            TenantUrlProvider tenantUrlProvider,
            IConfiguration configuration,
        IRepository<PermissionGrant, Guid> permissionrepo)
        {
            _guidGenerator = guidGenerator;
            _roleRepository = roleRepository;
            _permissionrepo = permissionrepo;
            _identityUserManager = identityUserManager;
            _emailHelper = emailHelper;
            _tenantUrlProvider = tenantUrlProvider;
            _configuration = configuration;
        }



        //CA Portal creation
        public async Task HandleEventAsync(NewCATenantCreatedEto eventData)
        {
            try
            {
                Logger.LogInformation("NewCATenantCreatedEto is handled", eventData);
                using (CurrentTenant.Change(eventData.TenantId))
                {
                    var tenantId = eventData.TenantId;
                    //System Administrator
                    var raSysAdminRole = new IdentityRole(_guidGenerator.Create(), BdoRoleConsts.CASystemAdminRoleName, tenantId);
                    await _roleRepository.InsertAsync(raSysAdminRole, true);

                    var systemAdminGrantList = new List<PermissionGrant> {
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Roles","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Roles.Create","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Roles.Update","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Roles.Delete","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Roles.ManagePermissions","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users.Create","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users.Update","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users.Delete","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users.ManagePermissions","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users.Update.ManageRoles","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.ViewHistory,"R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeDetail.ViewHistory, "R",BdoRoleConsts.CASystemAdminRoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.Default,"R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.SearchOwn,"R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.ViewOwn,"R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.SearchAll,"R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.ViewAll,"R", BdoRoleConsts.CASystemAdminRoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.MonitoringDashboard,"R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.RedFlagEvents,"R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.ManageRedFlagSetting,"R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.InformationExchange,"R", BdoRoleConsts.CASystemAdminRoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch.ESSearch","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch.AdvancedSearch","R", BdoRoleConsts.CASystemAdminRoleName, tenantId),

                        // Audit Permissions.
                        new PermissionGrant(_guidGenerator.Create(), AuditServicePermissions.AuditPermissions.Default, "R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditServicePermissions.AuditPermissions.ViewOwn, "R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditServicePermissions.AuditPermissions.SearchOwn, "R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditServicePermissions.AuditPermissions.ViewAll, "R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditServicePermissions.AuditPermissions.SearchAll, "R", BdoRoleConsts.CASystemAdminRoleName, tenantId),


                        // For RedFlag Permissions.
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.RedFlag.Default, "R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.RedFlag.View, "R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.RedFlag.Edit, "R", BdoRoleConsts.CASystemAdminRoleName, tenantId),

                        // For CtsIntgration Permissions.
                        new PermissionGrant(_guidGenerator.Create(), CtsIntegrationPermissions.CertificatePermissions.Default, "R", BdoRoleConsts.CASystemAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), CtsIntegrationPermissions.CertificatePermissions.Import, "R", BdoRoleConsts.CASystemAdminRoleName, tenantId),


                    };
                    await _permissionrepo.InsertManyAsync(systemAdminGrantList);

                    //CA
                    var caRole = new IdentityRole(_guidGenerator.Create(), BdoRoleConsts.CARoleName, tenantId);
                    await _roleRepository.InsertAsync(caRole, true);

                    var caGrantList = new List<PermissionGrant> {
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.AssignmentList.Default ,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.AssignmentList.View ,"R", BdoRoleConsts.CARoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.Default,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.ViewHistory,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.UpdateAssessment,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.AssessmentAction,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.AssessmentResubmission,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.CloseAssessment,"R", BdoRoleConsts.CARoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeDetail.Default,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeDetail.ViewHistory,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeDetail.Create,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeDetail.Edit,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeDetail.StatusAction,"R", BdoRoleConsts.CARoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeImportFile.Default,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeImportFile.Import,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeImportFile.Submit,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeImportFile.Discard,"R", BdoRoleConsts.CARoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.Default,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.SearchOwn,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.ViewOwn,"R", BdoRoleConsts.CARoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.MonitoringDashboard,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.RedFlagEvents,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.InformationExchange,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.SpontaneousInformationExchangeDetails,"R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.GenerateXML,"R", BdoRoleConsts.CARoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch","R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch.ESSearch","R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch.AdvancedSearch","R", BdoRoleConsts.CARoleName, tenantId),

                        // Audit Permissions.
                        new PermissionGrant(_guidGenerator.Create(), AuditServicePermissions.AuditPermissions.Default, "R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditServicePermissions.AuditPermissions.ViewOwn, "R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditServicePermissions.AuditPermissions.SearchOwn, "R", BdoRoleConsts.CARoleName, tenantId),
                  

                        // For RedFlag Permissions.
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.RedFlag.Default, "R", BdoRoleConsts.CARoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.RedFlag.View, "R", BdoRoleConsts.CARoleName, tenantId)
                    };
                    await _permissionrepo.InsertManyAsync(caGrantList);

                    //Legal Advisor
                    var legalAdvisorRole = new IdentityRole(_guidGenerator.Create(), BdoRoleConsts.LegalAdvisorRoleName, tenantId);
                    await _roleRepository.InsertAsync(legalAdvisorRole, true);

                    var legalAdvisorGrantList = new List<PermissionGrant> {
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.AssignmentList.Default ,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.AssignmentList.View ,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.Default,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.ViewHistory,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.UpdateAssessment,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.AssessmentAction,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.AssessmentResubmission,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.DeclarationAssessment.CloseAssessment,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeDetail.Default,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeDetail.ViewHistory,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeDetail.Create,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeDetail.Edit,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeDetail.StatusAction,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeImportFile.Default,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeImportFile.Import,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeImportFile.Submit,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.InformationExchangeImportFile.Discard,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.Default,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.SearchOwn,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.ViewOwn,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.MonitoringDashboard,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.RedFlagEvents,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.InformationExchange,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.SpontaneousInformationExchangeDetails,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), DashboardPermissions.GenerateXML,"R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch","R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch.ESSearch","R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch.AdvancedSearch","R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),

                        // Audit Permissions.
                        new PermissionGrant(_guidGenerator.Create(), AuditServicePermissions.AuditPermissions.Default, "R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditServicePermissions.AuditPermissions.ViewOwn, "R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditServicePermissions.AuditPermissions.SearchOwn, "R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                  

                        // For RedFlag Permissions.
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.RedFlag.Default, "R", BdoRoleConsts.LegalAdvisorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), ESServicePermissions.RedFlag.View, "R", BdoRoleConsts.LegalAdvisorRoleName, tenantId)
                    };
                    await _permissionrepo.InsertManyAsync(legalAdvisorGrantList);

                    //Auditor
                    var auditorRole = new IdentityRole(_guidGenerator.Create(), BdoRoleConsts.AuditorRoleName, tenantId);
                    await _roleRepository.InsertAsync(auditorRole, true);

                    var auditorGrantList = new List<PermissionGrant> {
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.Default,"R", BdoRoleConsts.AuditorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.SearchOwn,"R", BdoRoleConsts.AuditorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.ViewOwn,"R", BdoRoleConsts.AuditorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.SearchAll,"R", BdoRoleConsts.AuditorRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.ViewAll,"R", BdoRoleConsts.AuditorRoleName, tenantId),

                    };
                    await _permissionrepo.InsertManyAsync(auditorGrantList);
                }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
            }
        }

        //RA Portal creation
        public async Task HandleEventAsync(NewRATenantCreatedEto eventData)
        {
            try
            {
                Logger.LogInformation("NewRATenantCreatedEto is handled", eventData);
                using (CurrentTenant.Change(eventData.TenanId))
                {
                    var tenantId = eventData.TenanId;
                    var raSysAdminRole = new IdentityRole(_guidGenerator.Create(), BdoRoleConsts.RAAdminRoleName, tenantId);
                    await _roleRepository.InsertAsync(raSysAdminRole, true);

                    var adminPermissionGrantList = new List<PermissionGrant>{
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Roles","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Roles.Create","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Roles.Update","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Roles.Delete","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Roles.ManagePermissions","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users.Create","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users.Update","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users.Delete","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users.ManagePermissions","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "AbpIdentity.Users.Update.ManageRoles","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch.ESSearch","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch.ViewDeclaration","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SaasService.RaDetails","R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.Templates","R",BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.DeclarationImportFile.UploadTemplate","R",BdoRoleConsts.RAAdminRoleName, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.Default,"R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.SearchOwn,"R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.ViewOwn,"R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.SearchAll,"R", BdoRoleConsts.RAAdminRoleName, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.ViewAll,"R", BdoRoleConsts.RAAdminRoleName, tenantId),


                    };
                    await _permissionrepo.InsertManyAsync(adminPermissionGrantList);

                    var raUserRole = new IdentityRole(_guidGenerator.Create(), BdoRoleConsts.RAUserRole, tenantId);
                    await _roleRepository.InsertAsync(raUserRole, true);

                    var permissionGrantList = new List<PermissionGrant>{
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch","R", BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch.ESSearch","R", BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "SearchService.BasicSearch.ViewDeclaration","R", BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.Declaration","R", BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.Declaration.Submit","R", BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.Declaration.ViewHistoryStatus", "R", BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.Declaration.Resubmit", "R", BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.Declaration.SubmitInformation", "R", BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.Templates","R",BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.DeclarationImportFile","R",BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.DeclarationImportFile.Import","R",BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.DeclarationImport","R",BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.DeclarationImport.Submit","R",BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.DeclarationImport.Edit","R",BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), "EsService.DeclarationImport.Delete","R",BdoRoleConsts.RAUserRole, tenantId),

                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.Default,"R", BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.SearchOwn,"R", BdoRoleConsts.RAUserRole, tenantId),
                        new PermissionGrant(_guidGenerator.Create(), AuditPermissions.ViewOwn,"R", BdoRoleConsts.RAUserRole, tenantId),                    
                    };

                    await _permissionrepo.InsertManyAsync(permissionGrantList);

                    if (eventData.CreateAdminAccount && eventData.userInfo != null)
                    {
                        foreach (var user in eventData.userInfo.Users)
                        {
                            IdentityUser adminUser = new IdentityUser(_guidGenerator.Create(), user.UserName, user.Email, eventData.TenanId);
                            adminUser.Name = user.FirstName;
                            adminUser.Surname = user.LastName;
                            adminUser.AddRole(raSysAdminRole.Id);
                            adminUser.AddRole(raUserRole.Id);
                            adminUser.SetPhoneNumberConfirmed(true);
                            adminUser.SetEmailConfirmed(true);
                            adminUser.SetProperty("FirstLogin", true);
                            adminUser.SetShouldChangePasswordOnNextLogin(true);

                            var identity = await _identityUserManager.CreateAsync(adminUser);

                            if (!user.FirstName.IsNullOrEmpty())
                            {
                                await _identityUserManager.SetPhoneNumberAsync(adminUser, user.PhoneNumber);
                            }

                            var newTempPassword = new Password(includeLowercase: true, includeUppercase: true, includeNumeric: true, includeSpecial: true, passwordLength: 11);

                            var u = await _identityUserManager.GetByIdAsync(adminUser.Id);

                            var password = newTempPassword.Next();

                            var test = await _identityUserManager.AddPasswordAsync(adminUser, password);

                            await _emailHelper.newUserEmail(
                                        u.Email,
                                        u.UserName,
                                        u.Name + " " + u.Surname,
                                        password,
                                        await _tenantUrlProvider.GetUrlAsync(_configuration["AngularURL"]),
                                        user.PhoneNumber,
                                        $"{BdoRoleConsts.RAAdminRoleName}, {BdoRoleConsts.RAUserRole}");
                        }
                    }
                }
            
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
            }
        }

    }
}

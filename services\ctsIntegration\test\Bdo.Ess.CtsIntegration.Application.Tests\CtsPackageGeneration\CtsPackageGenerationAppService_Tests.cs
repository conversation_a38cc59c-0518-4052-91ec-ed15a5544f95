using System;
using System.IO;
using System.IO.Compression;
using System.Text;
using System.Threading.Tasks;
using Bdo.Ess.CtsIntegration.Certificate;
using Bdo.Ess.CtsIntegration.CtsApi;
using Bdo.Ess.CtsIntegration.CtsPackageGeneration;
using Bdo.Ess.CtsIntegration.CtsPackageRequests;
using Bdo.Ess.CtsIntegration.Encryption;
using Bdo.Ess.CtsIntegration.Entities;
using Bdo.Ess.CtsIntegration.PackageGeneration;
using Microsoft.Extensions.Configuration;
using NSubstitute;
using Shouldly;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;
using Xunit;

namespace Bdo.Ess.CtsIntegration.CtsPackageGeneration
{
    public class CtsPackageGenerationAppService_Tests : CtsIntegrationApplicationTestBase<CtsIntegrationApplicationTestModule>
    {
        private readonly CtsPackageGenerationAppService _appService;
        private readonly ICtsIntegrationBlobAppService _mockBlobAppService;
        private readonly IRepository<CtsPackageRequest, Guid> _mockCtsPackageRequestRepository;
        private readonly ICertificateAppService _mockCertificateAppService;
        private readonly ICountryCertificateAppService _mockCountryCertificateAppService;
        private readonly ICtsApiClient _mockCtsApiClient;
        private readonly ICtsEncryptionManager _mockEncryptionManager;
        private readonly ICurrentTenant _mockCurrentTenant;
        private readonly IDistributedEventBus _mockDistributedEventBus;
        private readonly IConfiguration _mockConfiguration;

        public CtsPackageGenerationAppService_Tests()
        {
            // Create mocks
            _mockBlobAppService = Substitute.For<ICtsIntegrationBlobAppService>();
            _mockCtsPackageRequestRepository = Substitute.For<IRepository<CtsPackageRequest, Guid>>();
            _mockCertificateAppService = Substitute.For<ICertificateAppService>();
            _mockCountryCertificateAppService = Substitute.For<ICountryCertificateAppService>();
            _mockCtsApiClient = Substitute.For<ICtsApiClient>();
            _mockEncryptionManager = Substitute.For<ICtsEncryptionManager>();
            _mockCurrentTenant = Substitute.For<ICurrentTenant>();
            _mockDistributedEventBus = Substitute.For<IDistributedEventBus>();
            _mockConfiguration = Substitute.For<IConfiguration>();

            // Setup mock configuration
            SetupMockConfiguration();

            // Create the service with mocked dependencies
            _appService = new CtsPackageGenerationAppService(
                _mockBlobAppService,
                _mockCtsPackageRequestRepository,
                _mockCertificateAppService,
                _mockCountryCertificateAppService,
                _mockCtsApiClient,
                _mockEncryptionManager,
                _mockCurrentTenant,
                _mockDistributedEventBus,
                _mockConfiguration
            );
        }

        [Fact]
        public async Task UnpackAsync_Should_Process_CtsPackage_Until_Decryption_Step()
        {
            // Arrange
            var validMetadataXml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<CTSSenderFileMetadata xmlns=""urn:oecd:ctssenderfilemetadata"">
    <CTSReceiverCountryCd>BS</CTSReceiverCountryCd>
    <CTSCommunicationTypeCd>NTJ</CTSCommunicationTypeCd>
    <CTSSenderCountryCd>US</CTSSenderCountryCd>
    <FileCreateTs>2024-01-15T10:30:00Z</FileCreateTs>
</CTSSenderFileMetadata>";

            var validPayloadContent = "Test payload content";

            var zipContent = CreateTestZipFile(validMetadataXml, validPayloadContent, "US");

            // Act & Assert - The method should process the package successfully until it reaches
            // the decryption step where it fails due to dummy certificate/key data
            var exception = await Should.ThrowAsync<UserFriendlyException>(
                async () => await _appService.UnpackAsync(zipContent));

            // Verify it gets to Step 3 (decryption) which means metadata validation passed
            exception.Message.ShouldContain("Step 3: Decrypt AES key and IV");
        }

        [Fact]
        public async Task UnpackAsync_Should_Validate_Metadata_Requirements()
        {
            // Arrange - Invalid receiver country code
            var invalidMetadataXml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<CTSSenderFileMetadata xmlns=""urn:oecd:ctssenderfilemetadata"">
    <CTSReceiverCountryCd>US</CTSReceiverCountryCd>
    <CTSCommunicationTypeCd>NTJ</CTSCommunicationTypeCd>
    <CTSSenderCountryCd>CA</CTSSenderCountryCd>
    <FileCreateTs>2024-01-15T10:30:00Z</FileCreateTs>
</CTSSenderFileMetadata>";

            var validPayloadContent = "Test payload content";

            var zipContent = CreateTestZipFile(invalidMetadataXml, validPayloadContent, "CA");

            // Act & Assert
            var exception = await Should.ThrowAsync<Volo.Abp.UserFriendlyException>(
                async () => await _appService.UnpackAsync(zipContent)
            );

            exception.Message.ShouldContain("Invalid receiver country code", Case.Insensitive);
        }

        [Fact]
        public async Task UnpackAsync_Should_Handle_Missing_Required_Files()
        {
            // Arrange - Create zip with only metadata.xml, missing payload.xml
            var validMetadataXml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<CTSSenderFileMetadata xmlns=""urn:oecd:ctssenderfilemetadata"">
    <CTSReceiverCountryCd>BS</CTSReceiverCountryCd>
    <CTSCommunicationTypeCd>NTJ</CTSCommunicationTypeCd>
    <CTSSenderCountryCd>US</CTSSenderCountryCd>
    <FileCreateTs>2024-01-15T10:30:00Z</FileCreateTs>
</CTSSenderFileMetadata>";

            var zipContent = CreateTestZipFileWithMissingPayload(validMetadataXml);

            // Act & Assert
            var exception = await Should.ThrowAsync<Volo.Abp.UserFriendlyException>(
                async () => await _appService.UnpackAsync(zipContent)
            );

            exception.Message.ShouldContain("Payload file not found", Case.Insensitive);
        }

        [Fact]
        public void ValidateXml_Should_Pass_When_ValidXml()
        {
            // Arrange
            var validXml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<CTSMessageSpec xmlns=""urn:oecd:ties:ctsschema:v1"">
    <MessageSpec>
        <MessageType>NTJMessage</MessageType>
        <SendingEntityIN>*********</SendingEntityIN>
        <TransmittingCountry>BS</TransmittingCountry>
        <ReceivingCountry>BS</ReceivingCountry>
        <MessageTypeIndic>CTS401</MessageTypeIndic>
        <ReportingPeriod>2023-12-31</ReportingPeriod>
        <Timestamp>2024-01-15T10:30:00Z</Timestamp>
    </MessageSpec>
</CTSMessageSpec>";

            var xmlBytes = Encoding.UTF8.GetBytes(validXml);

            // Act & Assert - Should not throw (ValidateXml is a static method)
            Should.NotThrow(() => CtsPackageGenerationAppService.ValidateXml(xmlBytes));
        }

        [Fact]
        public async Task GeneratePackageAsync_Should_Generate_Package_When_ValidInput()
        {
            // Arrange
            var packageRequestDto = new CtsPackageRequestDataDto
            {
                Id = Guid.NewGuid(),
                ReceiverCountryCode = "US",
                FiscalYear = DateTime.Now.Year,
                XmlPayloadUrl = "test-url",
                TenantId = Guid.NewGuid()
            };

            var mockRequest = new CtsPackageRequest(packageRequestDto.Id)
            {
                ReceiverCountryCode = "US",
                FiscalYear = DateTime.Now.Year,
                XmlPayloadUrl = "test-url",
                TenantId = packageRequestDto.TenantId
            };

            _mockCtsPackageRequestRepository.GetAsync(packageRequestDto.Id).Returns(mockRequest);
            _mockCtsPackageRequestRepository.UpdateAsync(Arg.Any<CtsPackageRequest>()).Returns(mockRequest);

            // Mock blob service to return encrypted XML content
            var testXmlContent = "encrypted-xml-content";
            _mockBlobAppService.DownloadFileBytes(Arg.Any<string>()).Returns(Encoding.UTF8.GetBytes(testXmlContent));

            // Mock encryption manager
            _mockEncryptionManager.DecryptStr(Arg.Any<string>()).Returns("<test>xml</test>");

            // Act - The method should not throw exceptions, it handles them gracefully
            var result = await _appService.GeneratePackageAsync(packageRequestDto);

            // Assert - Should return a result (could be empty string if there are issues)
            result.ShouldNotBeNull();

            // Verify that the repository was called
            await _mockCtsPackageRequestRepository.Received().GetAsync(packageRequestDto.Id);
        }

        /// <summary>
        /// Sets up mock configuration
        /// </summary>
        private void SetupMockConfiguration()
        {
            // Setup the configuration value directly - return null so default value is used
            _mockConfiguration["Cts:DebugIntermFiles"].Returns((string)null);

            // Setup configuration section for Cts
            var ctsSection = Substitute.For<IConfigurationSection>();
            ctsSection["DebugIntermFiles"].Returns((string)null);
            ctsSection.Value.Returns((string)null);
            _mockConfiguration.GetSection("Cts").Returns(ctsSection);

            // Setup other configuration values that might be used
            _mockConfiguration["Cts:UseTestCountry"].Returns((string)null);
            _mockConfiguration["Cts:Api:CountriesUseHub"].Returns((string)null);
            
            // For any other keys, return null so default values are used
            _mockConfiguration[Arg.Any<string>()].Returns((string)null);

            // Setup certificate service mock
            var mockCertificate = new BahamasCertificateDto
            {
                Id = Guid.NewGuid(),
                CertificateContent = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("dummy-pfx-content")),
                CertificatePassword = "dummy-password",
                PublicKey = "dummy-public-key",
                CertificateContentType = "application/x-pkcs12",
                CertificateFileName = "test.pfx",
                ExpiredAt = DateTime.UtcNow.AddYears(1),
                ValidFrom = DateTime.UtcNow.AddYears(-1),
                IsActive = true
            };

            _mockCertificateAppService.GetBahamasCertificateByCreationTimeAsync(Arg.Any<DateTime>())
                .Returns(Task.FromResult<BahamasCertificateDto?>(mockCertificate));

            // Also mock GetBahamasCertificateInfo for GeneratePackageAsync
            _mockCertificateAppService.GetBahamasCertificateInfo()
                .Returns(Task.FromResult<BahamasCertificateDto?>(mockCertificate));

            // Setup country certificate service mock
            var mockCountryCertificate = new CountryCertificateDto
            {
                Id = Guid.NewGuid(),
                CountryCode = "US",
                PublicKey = "dummy-country-public-key"
            };

            _mockCountryCertificateAppService.GetByCountryCode2ByCreationTimeAsync(Arg.Any<string>(), Arg.Any<DateTime>())
                .Returns(Task.FromResult<CountryCertificateDto?>(mockCountryCertificate));

            // Setup CTS API client mock
            _mockCtsApiClient.GetCertificateAsync(Arg.Any<string>())
                .Returns(Task.FromResult<string?>("dummy-certificate-base64"));
        }

        /// <summary>
        /// Creates a test zip file with CTS naming convention
        /// </summary>
        private byte[] CreateTestZipFile(string metadataXml, string payloadContent, string senderCountryCode)
        {
            using var memoryStream = new MemoryStream();
            using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
            {
                // Add metadata file with CTS naming: <SenderCountry>_NTJ_Metadata.xml
                var metadataEntry = archive.CreateEntry($"{senderCountryCode}_NTJ_Metadata.xml");
                using (var entryStream = metadataEntry.Open())
                using (var writer = new StreamWriter(entryStream))
                {
                    writer.Write(metadataXml);
                }

                // Add payload file with CTS naming: <SenderCountry>_NTJ_Payload
                var payloadEntry = archive.CreateEntry($"{senderCountryCode}_NTJ_Payload");
                using (var entryStream = payloadEntry.Open())
                using (var writer = new StreamWriter(entryStream))
                {
                    writer.Write(payloadContent);
                }

                // Add key file with CTS naming: BS_NTJ_Key
                var keyEntry = archive.CreateEntry("BS_NTJ_Key");
                using (var entryStream = keyEntry.Open())
                using (var writer = new StreamWriter(entryStream))
                {
                    writer.Write("dummy-key-content");
                }
            }

            return memoryStream.ToArray();
        }

        /// <summary>
        /// Creates a test zip file with only metadata (missing payload)
        /// </summary>
        private byte[] CreateTestZipFileWithMissingPayload(string metadataXml)
        {
            using var memoryStream = new MemoryStream();
            using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
            {
                // Add only metadata file with CTS naming: US_NTJ_Metadata.xml
                var metadataEntry = archive.CreateEntry("US_NTJ_Metadata.xml");
                using (var entryStream = metadataEntry.Open())
                using (var writer = new StreamWriter(entryStream))
                {
                    writer.Write(metadataXml);
                }

                // Add key file but no payload file
                var keyEntry = archive.CreateEntry("BS_NTJ_Key");
                using (var entryStream = keyEntry.Open())
                using (var writer = new StreamWriter(entryStream))
                {
                    writer.Write("dummy-key-content");
                }
            }

            return memoryStream.ToArray();
        }

        /// <summary>
        /// Creates an incomplete zip file for testing missing files
        /// </summary>
        private byte[] CreateIncompleteZipFile()
        {
            using var memoryStream = new MemoryStream();
            using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
            {
                // Add only a dummy file, no CTS files
                var dummyEntry = archive.CreateEntry("dummy.txt");
                using (var entryStream = dummyEntry.Open())
                using (var writer = new StreamWriter(entryStream))
                {
                    writer.Write("dummy content");
                }
            }

            return memoryStream.ToArray();
        }
    }
}

using Bdo.Ess.CtsIntegration.Certificate;
using Bdo.Ess.CtsIntegration.CtsApi;
using Bdo.Ess.CtsIntegration.CtsPackageGeneration;
using Bdo.Ess.CtsIntegration.Encryption;
using Bdo.Ess.CtsIntegration.Entities;
using Bdo.Ess.CtsIntegration.PackageGeneration;
using Bdo.Ess.Shared.Constants.InformationExchanges;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Modularity;
using Xunit;

namespace Bdo.Ess.CtsIntegration.CtsPackageGeneration
{
    public class CtsPackageGenerationAppService_Tests : CtsIntegrationApplicationTestBase<CtsIntegrationApplicationTestModule>
    {
        private readonly ICtsIntegrationBlobAppService _mockBlobAppService;
        private readonly IRepository<CtsPackageRequest, Guid> _mockRepository;
        private readonly ICertificateAppService _mockCertificateAppService;
        private readonly ICountryCertificateAppService _mockCountryCertificateAppService;
        private readonly ICtsApiClient _mockCtsApiClient;
        private readonly ICtsEncryptionManager _mockEncryptionManager;
        private readonly IDistributedEventBus _mockDistributedEventBus;
        private readonly IConfiguration _mockConfiguration;
        private readonly ICurrentTenant _mockCurrentTenant;
        private readonly CtsPackageGenerationAppService _appService;

        public CtsPackageGenerationAppService_Tests()
        {
            _mockBlobAppService = Substitute.For<ICtsIntegrationBlobAppService>();
            _mockRepository = Substitute.For<IRepository<CtsPackageRequest, Guid>>();
            _mockCertificateAppService = Substitute.For<ICertificateAppService>();
            _mockCountryCertificateAppService = Substitute.For<ICountryCertificateAppService>();
            _mockCtsApiClient = Substitute.For<ICtsApiClient>();
            _mockEncryptionManager = Substitute.For<ICtsEncryptionManager>();
            _mockDistributedEventBus = Substitute.For<IDistributedEventBus>();
            _mockConfiguration = Substitute.For<IConfiguration>();
            _mockCurrentTenant = Substitute.For<ICurrentTenant>();

            _appService = new CtsPackageGenerationAppService(
                _mockBlobAppService,
                _mockRepository,
                _mockCertificateAppService,
                _mockCountryCertificateAppService,
                _mockCtsApiClient,
                _mockEncryptionManager,
                _mockCurrentTenant,
                _mockDistributedEventBus,
                _mockConfiguration,
                GetRequiredService<ILogger<CtsPackageGenerationAppService>>()
            );

            SetupBasicMocks();
        }

        private void SetupBasicMocks()
        {
            _mockCurrentTenant.Id.Returns(Guid.NewGuid());
            _mockConfiguration["CTS:DigitalSigningSettings:SignedElementId"]
                .Returns("CompetentAuthoritySignature");
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_WithValidParameters_ShouldCreateInstance()
        {
            // Arrange & Act
            var service = new CtsPackageGenerationAppService(
                _mockBlobAppService,
                _mockRepository,
                _mockCertificateAppService,
                _mockCountryCertificateAppService,
                _mockCtsApiClient,
                _mockEncryptionManager,
                _mockCurrentTenant,
                _mockDistributedEventBus,
                _mockConfiguration,
                GetRequiredService<ILogger<CtsPackageGenerationAppService>>()
            );

            // Assert
            service.ShouldNotBeNull();
        }

        [Fact]
        public void Constructor_WithNullBlobAppService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Should.Throw<ArgumentNullException>(() => new CtsPackageGenerationAppService(
                null!,
                _mockRepository,
                _mockCertificateAppService,
                _mockCountryCertificateAppService,
                _mockCtsApiClient,
                _mockEncryptionManager,
                _mockCurrentTenant,
                _mockDistributedEventBus,
                _mockConfiguration,
                GetRequiredService<ILogger<CtsPackageGenerationAppService>>()
            ));
        }

        [Fact]
        public void Constructor_WithNullRepository_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Should.Throw<ArgumentNullException>(() => new CtsPackageGenerationAppService(
                _mockBlobAppService,
                null!,
                _mockCertificateAppService,
                _mockCountryCertificateAppService,
                _mockCtsApiClient,
                _mockEncryptionManager,
                _mockCurrentTenant,
                _mockDistributedEventBus,
                _mockConfiguration,
                GetRequiredService<ILogger<CtsPackageGenerationAppService>>()
            ));
        }

        #endregion

        #region GeneratePackageAsync Tests

        [Fact]
        public async Task GeneratePackageAsync_WithValidPackageRequestId_ShouldReturnSuccessResult()
        {
            // Arrange
            var packageRequestId = Guid.NewGuid();
            var packageRequest = CreateTestPackageRequest(packageRequestId);
            var xmlContent = CreateTestXmlContent();
            var certificate = CreateTestCertificate();

            _mockRepository.GetAsync(packageRequestId, true, Arg.Any<CancellationToken>())
                .Returns(packageRequest);
            
            _mockBlobAppService.GetBlobContentAsync(Arg.Any<string>())
                .Returns(Encoding.UTF8.GetBytes(xmlContent));

            _mockCertificateAppService.GetCertificateFromStoreAsync(Arg.Any<string>())
                .Returns(certificate);

            _mockCountryCertificateAppService.GetCountryCertificateAsync(Arg.Any<string>())
                .Returns(certificate);

            _mockBlobAppService.UploadBlobAsync(Arg.Any<string>(), Arg.Any<byte[]>())
                .Returns("test-blob-url");

            // Act
            var result = await _appService.GeneratePackageAsync(packageRequestId);

            // Assert
            result.ShouldNotBeNull();
            result.Success.ShouldBeTrue();
            result.Message.ShouldNotBeNullOrEmpty();
        }

        [Fact]
        public async Task GeneratePackageAsync_WithInvalidPackageRequestId_ShouldThrowException()
        {
            // Arrange
            var invalidId = Guid.NewGuid();
            _mockRepository.GetAsync(invalidId, true, Arg.Any<CancellationToken>())
                .Returns<CtsPackageRequest>(x => { throw new InvalidOperationException("Package request not found"); });

            // Act & Assert
            await Should.ThrowAsync<InvalidOperationException>(
                () => _appService.GeneratePackageAsync(invalidId)
            );
        }

        [Fact]
        public async Task GeneratePackageAsync_WithXmlPayloadUrl_ShouldLoadXmlFromBlob()
        {
            // Arrange
            var packageRequestId = Guid.NewGuid();
            var packageRequest = CreateTestPackageRequest(packageRequestId);
            packageRequest.XmlPayloadUrl = "test-blob-url";
            packageRequest.XmlPayload = null;

            var xmlContent = CreateTestXmlContent();
            var certificate = CreateTestCertificate();

            _mockRepository.GetAsync(packageRequestId, true, Arg.Any<CancellationToken>())
                .Returns(packageRequest);

            _mockBlobAppService.GetBlobContentAsync("test-blob-url")
                .Returns(Encoding.UTF8.GetBytes(xmlContent));

            _mockCertificateAppService.GetCertificateFromStoreAsync(Arg.Any<string>())
                .Returns(certificate);

            _mockCountryCertificateAppService.GetCountryCertificateAsync(Arg.Any<string>())
                .Returns(certificate);

            _mockBlobAppService.UploadBlobAsync(Arg.Any<string>(), Arg.Any<byte[]>())
                .Returns("generated-package-url");

            // Act
            var result = await _appService.GeneratePackageAsync(packageRequestId);

            // Assert
            await _mockBlobAppService.Received(1).GetBlobContentAsync("test-blob-url");
            result.Success.ShouldBeTrue();
        }

        [Fact]
        public async Task GeneratePackageAsync_WithDirectXmlPayload_ShouldUseInMemoryXml()
        {
            // Arrange
            var packageRequestId = Guid.NewGuid();
            var xmlContent = CreateTestXmlContent();
            var packageRequest = CreateTestPackageRequest(packageRequestId);
            packageRequest.XmlPayload = xmlContent;
            packageRequest.XmlPayloadUrl = null;

            var certificate = CreateTestCertificate();

            _mockRepository.GetAsync(packageRequestId, true, Arg.Any<CancellationToken>())
                .Returns(packageRequest);

            _mockCertificateAppService.GetCertificateFromStoreAsync(Arg.Any<string>())
                .Returns(certificate);

            _mockCountryCertificateAppService.GetCountryCertificateAsync(Arg.Any<string>())
                .Returns(certificate);

            _mockBlobAppService.UploadBlobAsync(Arg.Any<string>(), Arg.Any<byte[]>())
                .Returns("generated-package-url");

            // Act
            var result = await _appService.GeneratePackageAsync(packageRequestId);

            // Assert
            await _mockBlobAppService.DidNotReceive().GetBlobContentAsync(Arg.Any<string>());
            result.Success.ShouldBeTrue();
        }

        #endregion

        #region GeneratePackageAsync with XML Parameter Tests

        [Fact]
        public async Task GeneratePackageAsync_WithPackageRequestAndXml_ShouldReturnSuccessResult()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(Guid.NewGuid());
            var xmlContent = CreateTestXmlContent();
            var certificate = CreateTestCertificate();

            _mockCertificateAppService.Setup(x => x.GetCertificateFromStoreAsync(It.IsAny<string>()))
                .ReturnsAsync(certificate);

            _mockCountryCertificateAppService.Setup(x => x.GetCountryCertificateAsync(It.IsAny<string>()))
                .ReturnsAsync(certificate);

            _mockBlobAppService.Setup(x => x.UploadBlobAsync(It.IsAny<string>(), It.IsAny<byte[]>()))
                .ReturnsAsync("generated-package-url");

            // Act
            var result = await _appService.GeneratePackageAsync(packageRequest, xmlContent);

            // Assert
            result.ShouldNotBeNull();
            result.Success.ShouldBeTrue();
            result.Message.ShouldNotBeNullOrEmpty();
        }

        [Fact]
        public async Task GeneratePackageAsync_WithNullPackageRequest_ShouldThrowArgumentNullException()
        {
            // Arrange
            var xmlContent = CreateTestXmlContent();

            // Act & Assert
            await Should.ThrowAsync<ArgumentNullException>(
                () => _appService.GeneratePackageAsync(null!, xmlContent)
            );
        }

        [Fact]
        public async Task GeneratePackageAsync_WithNullOrEmptyXml_ShouldThrowArgumentException()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(Guid.NewGuid());

            // Act & Assert
            await Should.ThrowAsync<ArgumentException>(
                () => _appService.GeneratePackageAsync(packageRequest, null!)
            );

            await Should.ThrowAsync<ArgumentException>(
                () => _appService.GeneratePackageAsync(packageRequest, "")
            );

            await Should.ThrowAsync<ArgumentException>(
                () => _appService.GeneratePackageAsync(packageRequest, "   ")
            );
        }

        #endregion

        #region GeneratePackageAsync with All Parameters Tests

        [Fact]
        public async Task GeneratePackageAsync_WithAllParameters_ShouldReturnSuccessResult()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(Guid.NewGuid());
            var xmlContent = CreateTestXmlContent();
            var certificate = CreateTestCertificate();
            var updateEntity = true;

            _mockCertificateAppService.Setup(x => x.GetCertificateFromStoreAsync(It.IsAny<string>()))
                .ReturnsAsync(certificate);

            _mockCountryCertificateAppService.Setup(x => x.GetCountryCertificateAsync(It.IsAny<string>()))
                .ReturnsAsync(certificate);

            _mockBlobAppService.Setup(x => x.UploadBlobAsync(It.IsAny<string>(), It.IsAny<byte[]>()))
                .ReturnsAsync("generated-package-url");

            // Act
            var result = await _appService.GeneratePackageAsync(packageRequest, xmlContent, updateEntity);

            // Assert
            result.ShouldNotBeNull();
            result.Success.ShouldBeTrue();
            result.Message.ShouldNotBeNullOrEmpty();
        }

        [Fact]
        public async Task GeneratePackageAsync_WithUpdateEntityTrue_ShouldUpdatePackageRequest()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(Guid.NewGuid());
            var xmlContent = CreateTestXmlContent();
            var certificate = CreateTestCertificate();
            var updateEntity = true;

            _mockCertificateAppService.Setup(x => x.GetCertificateFromStoreAsync(It.IsAny<string>()))
                .ReturnsAsync(certificate);

            _mockCountryCertificateAppService.Setup(x => x.GetCountryCertificateAsync(It.IsAny<string>()))
                .ReturnsAsync(certificate);

            _mockBlobAppService.Setup(x => x.UploadBlobAsync(It.IsAny<string>(), It.IsAny<byte[]>()))
                .ReturnsAsync("generated-package-url");

            // Act
            var result = await _appService.GeneratePackageAsync(packageRequest, xmlContent, updateEntity);

            // Assert
            _mockRepository.Verify(x => x.UpdateAsync(packageRequest, true, default), Times.Once);
            result.Success.ShouldBeTrue();
        }

        [Fact]
        public async Task GeneratePackageAsync_WithUpdateEntityFalse_ShouldNotUpdatePackageRequest()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(Guid.NewGuid());
            var xmlContent = CreateTestXmlContent();
            var certificate = CreateTestCertificate();
            var updateEntity = false;

            _mockCertificateAppService.Setup(x => x.GetCertificateFromStoreAsync(It.IsAny<string>()))
                .ReturnsAsync(certificate);

            _mockCountryCertificateAppService.Setup(x => x.GetCountryCertificateAsync(It.IsAny<string>()))
                .ReturnsAsync(certificate);

            _mockBlobAppService.Setup(x => x.UploadBlobAsync(It.IsAny<string>(), It.IsAny<byte[]>()))
                .ReturnsAsync("generated-package-url");

            // Act
            var result = await _appService.GeneratePackageAsync(packageRequest, xmlContent, updateEntity);

            // Assert
            _mockRepository.Verify(x => x.UpdateAsync(It.IsAny<CtsPackageRequest>(), It.IsAny<bool>(), default), Times.Never);
            result.Success.ShouldBeTrue();
        }

        #endregion

        #region UnpackAsync Tests

        [Fact]
        public async Task UnpackAsync_WithValidStream_ShouldReturnUnpackResult()
        {
            // Arrange
            var zipData = CreateTestZipData();
            using var stream = new MemoryStream(zipData);
            var encryptedPassword = "encrypted-password";
            var decryptedPassword = "test-password-123";

            _mockEncryptionManager.Setup(x => x.DecryptAesKey(encryptedPassword))
                .Returns(decryptedPassword);

            // Act
            var result = await _appService.UnpackAsync(stream, encryptedPassword);

            // Assert
            result.ShouldNotBeNull();
            result.Success.ShouldBeTrue();
        }

        [Fact]
        public async Task UnpackAsync_WithNullStream_ShouldThrowArgumentNullException()
        {
            // Arrange
            var encryptedPassword = "encrypted-password";

            // Act & Assert
            await Should.ThrowAsync<ArgumentNullException>(
                () => _appService.UnpackAsync(null!, encryptedPassword)
            );
        }

        [Fact]
        public async Task UnpackAsync_WithNullOrEmptyPassword_ShouldThrowArgumentException()
        {
            // Arrange
            var zipData = CreateTestZipData();
            using var stream = new MemoryStream(zipData);

            // Act & Assert
            await Should.ThrowAsync<ArgumentException>(
                () => _appService.UnpackAsync(stream, null!)
            );

            await Should.ThrowAsync<ArgumentException>(
                () => _appService.UnpackAsync(stream, "")
            );
        }

        [Fact]
        public async Task UnpackAsync_WithInvalidZipData_ShouldReturnFailureResult()
        {
            // Arrange
            var invalidZipData = Encoding.UTF8.GetBytes("Invalid ZIP data");
            using var stream = new MemoryStream(invalidZipData);
            var encryptedPassword = "encrypted-password";
            var decryptedPassword = "test-password-123";

            _mockEncryptionManager.Setup(x => x.DecryptAesKey(encryptedPassword))
                .Returns(decryptedPassword);

            // Act
            var result = await _appService.UnpackAsync(stream, encryptedPassword);

            // Assert
            result.ShouldNotBeNull();
            result.Success.ShouldBeFalse();
            result.Message.ShouldContain("error", StringComparison.OrdinalIgnoreCase);
        }

        #endregion

        #region UnpackAsync with Package Request Tests

        [Fact]
        public async Task UnpackAsync_WithValidPackageRequest_ShouldReturnUnpackResult()
        {
            // Arrange
            var packageRequestId = Guid.NewGuid();
            var packageRequest = CreateTestPackageRequest(packageRequestId);
            packageRequest.PackageZipUrl = "test-zip-url";
            
            var zipData = CreateTestZipData();
            var encryptedPassword = "encrypted-password";
            var decryptedPassword = "test-password-123";

            _mockRepository.Setup(x => x.GetAsync(packageRequestId, true, default))
                .ReturnsAsync(packageRequest);

            _mockBlobAppService.Setup(x => x.GetBlobContentAsync("test-zip-url"))
                .ReturnsAsync(zipData);

            _mockEncryptionManager.Setup(x => x.DecryptAesKey(encryptedPassword))
                .Returns(decryptedPassword);

            // Act
            var result = await _appService.UnpackAsync(packageRequestId, encryptedPassword);

            // Assert
            result.ShouldNotBeNull();
            result.Success.ShouldBeTrue();
        }

        [Fact]
        public async Task UnpackAsync_WithInvalidPackageRequestId_ShouldThrowException()
        {
            // Arrange
            var invalidId = Guid.NewGuid();
            var encryptedPassword = "encrypted-password";

            _mockRepository.Setup(x => x.GetAsync(invalidId, true, default))
                .ThrowsAsync(new InvalidOperationException("Package request not found"));

            // Act & Assert
            await Should.ThrowAsync<InvalidOperationException>(
                () => _appService.UnpackAsync(invalidId, encryptedPassword)
            );
        }

        [Fact]
        public async Task UnpackAsync_WithEmptyPackageZipUrl_ShouldThrowInvalidOperationException()
        {
            // Arrange
            var packageRequestId = Guid.NewGuid();
            var packageRequest = CreateTestPackageRequest(packageRequestId);
            packageRequest.PackageZipUrl = null;
            var encryptedPassword = "encrypted-password";

            _mockRepository.Setup(x => x.GetAsync(packageRequestId, true, default))
                .ReturnsAsync(packageRequest);

            // Act & Assert
            await Should.ThrowAsync<InvalidOperationException>(
                () => _appService.UnpackAsync(packageRequestId, encryptedPassword)
            );
        }

        #endregion

        #region Exception Handling Tests

        [Fact]
        public async Task GeneratePackageAsync_WhenCertificateServiceThrows_ShouldReturnFailureResult()
        {
            // Arrange
            var packageRequestId = Guid.NewGuid();
            var packageRequest = CreateTestPackageRequest(packageRequestId);
            var xmlContent = CreateTestXmlContent();

            _mockRepository.Setup(x => x.GetAsync(packageRequestId, true, default))
                .ReturnsAsync(packageRequest);

            _mockBlobAppService.Setup(x => x.GetBlobContentAsync(It.IsAny<string>()))
                .ReturnsAsync(Encoding.UTF8.GetBytes(xmlContent));

            _mockCertificateAppService.Setup(x => x.GetCertificateFromStoreAsync(It.IsAny<string>()))
                .ThrowsAsync(new InvalidOperationException("Certificate not found"));

            // Act
            var result = await _appService.GeneratePackageAsync(packageRequestId);

            // Assert
            result.ShouldNotBeNull();
            result.Success.ShouldBeFalse();
            result.Message.ShouldContain("Certificate not found");
        }

        [Fact]
        public async Task GeneratePackageAsync_WhenBlobServiceThrows_ShouldReturnFailureResult()
        {
            // Arrange
            var packageRequestId = Guid.NewGuid();
            var packageRequest = CreateTestPackageRequest(packageRequestId);

            _mockRepository.Setup(x => x.GetAsync(packageRequestId, true, default))
                .ReturnsAsync(packageRequest);

            _mockBlobAppService.Setup(x => x.GetBlobContentAsync(It.IsAny<string>()))
                .ThrowsAsync(new InvalidOperationException("Blob not found"));

            // Act
            var result = await _appService.GeneratePackageAsync(packageRequestId);

            // Assert
            result.ShouldNotBeNull();
            result.Success.ShouldBeFalse();
            result.Message.ShouldContain("Blob not found");
        }

        [Fact]
        public async Task UnpackAsync_WhenEncryptionManagerThrows_ShouldReturnFailureResult()
        {
            // Arrange
            var zipData = CreateTestZipData();
            using var stream = new MemoryStream(zipData);
            var encryptedPassword = "encrypted-password";

            _mockEncryptionManager.Setup(x => x.DecryptAesKey(encryptedPassword))
                .Throws(new InvalidOperationException("Decryption failed"));

            // Act
            var result = await _appService.UnpackAsync(stream, encryptedPassword);

            // Assert
            result.ShouldNotBeNull();
            result.Success.ShouldBeFalse();
            result.Message.ShouldContain("Decryption failed");
        }

        #endregion

        #region Integration Tests

        [Fact]
        public async Task GeneratePackageAsync_EndToEndFlow_ShouldCompleteSuccessfully()
        {
            // Arrange
            var packageRequestId = Guid.NewGuid();
            var packageRequest = CreateTestPackageRequest(packageRequestId);
            var xmlContent = CreateTestXmlContent();
            var certificate = CreateTestCertificate();

            _mockRepository.Setup(x => x.GetAsync(packageRequestId, true, default))
                .ReturnsAsync(packageRequest);

            _mockBlobAppService.Setup(x => x.GetBlobContentAsync(It.IsAny<string>()))
                .ReturnsAsync(Encoding.UTF8.GetBytes(xmlContent));

            _mockCertificateAppService.Setup(x => x.GetCertificateFromStoreAsync(It.IsAny<string>()))
                .ReturnsAsync(certificate);

            _mockCountryCertificateAppService.Setup(x => x.GetCountryCertificateAsync(It.IsAny<string>()))
                .ReturnsAsync(certificate);

            _mockBlobAppService.Setup(x => x.UploadBlobAsync(It.IsAny<string>(), It.IsAny<byte[]>()))
                .ReturnsAsync("generated-package-url");

            _mockDistributedEventBus.Setup(x => x.PublishAsync(It.IsAny<object>(), It.IsAny<bool>(), It.IsAny<bool>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _appService.GeneratePackageAsync(packageRequestId);

            // Assert
            result.ShouldNotBeNull();
            result.Success.ShouldBeTrue();
            result.Message.ShouldNotBeNullOrEmpty();

            // Verify all services were called
            _mockRepository.Verify(x => x.GetAsync(packageRequestId, true, default), Times.Once);
            _mockBlobAppService.Verify(x => x.GetBlobContentAsync(It.IsAny<string>()), Times.AtLeastOnce);
            _mockCertificateAppService.Verify(x => x.GetCertificateFromStoreAsync(It.IsAny<string>()), Times.Once);
            _mockCountryCertificateAppService.Verify(x => x.GetCountryCertificateAsync(It.IsAny<string>()), Times.Once);
            _mockBlobAppService.Verify(x => x.UploadBlobAsync(It.IsAny<string>(), It.IsAny<byte[]>()), Times.Once);
            _mockRepository.Verify(x => x.UpdateAsync(packageRequest, true, default), Times.Once);
        }

        #endregion

        #region Helper Methods

        private CtsPackageRequest CreateTestPackageRequest(Guid id)
        {
            return new CtsPackageRequest(id)
            {
                ReceiverCountryCode = "US",
                FiscalYear = 2023,
                ExchangeReason = ExchangeReason.CRS,
                XmlPayload = CreateTestXmlContent(),
                XmlPayloadUrl = "test-xml-url",
                MessageRefId = "TEST-MSG-001",
                TenantId = Guid.NewGuid(),
                HasExchangeRecords = true
            };
        }

        private string CreateTestXmlContent()
        {
            return @"<?xml version='1.0' encoding='UTF-8'?>
<crs:CRS_OECD xmlns:crs='urn:oecd:ties:crs:v1' 
             xmlns:stf='urn:oecd:ties:stf:v4' 
             version='1.0'>
  <crs:MessageSpec>
    <stf:SendingCompetentAuthority>BS</stf:SendingCompetentAuthority>
    <stf:ReceivingCompetentAuthority>US</stf:ReceivingCompetentAuthority>
    <stf:MessageType>CRS</stf:MessageType>
    <stf:MessageRefId>TEST-MSG-001</stf:MessageRefId>
    <stf:Timestamp>2023-01-01T00:00:00</stf:Timestamp>
  </crs:MessageSpec>
  <crs:CrsBody>
    <crs:ReportingGroup>
      <crs:Sponsor>
        <crs:Name>Test Sponsor</crs:Name>
      </crs:Sponsor>
    </crs:ReportingGroup>
  </crs:CrsBody>
</crs:CRS_OECD>";
        }

        private System.Security.Cryptography.X509Certificates.X509Certificate2 CreateTestCertificate()
        {
            using var rsa = RSA.Create(2048);
            var req = new CertificateRequest("CN=TestCertificate", rsa, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            var cert = req.CreateSelfSigned(DateTimeOffset.Now, DateTimeOffset.Now.AddYears(1));
            return new X509Certificate2(cert.Export(X509ContentType.Pfx), (string?)null, X509KeyStorageFlags.Exportable);
        }

        private byte[] CreateTestZipData()
        {
            // Create minimal valid ZIP file data
            // This is a simplified representation - in real tests you might want to create an actual ZIP
            using var memoryStream = new MemoryStream();
            using var writer = new BinaryWriter(memoryStream);
            
            // ZIP local file header signature
            writer.Write(0x04034b50);
            writer.Write((short)20); // Version needed to extract
            writer.Write((short)0);  // General purpose bit flag
            writer.Write((short)0);  // Compression method (stored)
            writer.Write((short)0);  // File last modification time
            writer.Write((short)0);  // File last modification date
            writer.Write(0);         // CRC-32
            writer.Write(0);         // Compressed size
            writer.Write(0);         // Uncompressed size
            writer.Write((short)8);  // File name length
            writer.Write((short)0);  // Extra field length
            writer.Write(Encoding.UTF8.GetBytes("test.txt"));

            // ZIP central directory file header
            writer.Write(0x02014b50); // Central file header signature
            writer.Write((short)20);   // Version made by
            writer.Write((short)20);   // Version needed to extract
            writer.Write((short)0);    // General purpose bit flag
            writer.Write((short)0);    // Compression method
            writer.Write((short)0);    // File last modification time
            writer.Write((short)0);    // File last modification date
            writer.Write(0);           // CRC-32
            writer.Write(0);           // Compressed size
            writer.Write(0);           // Uncompressed size
            writer.Write((short)8);    // File name length
            writer.Write((short)0);    // Extra field length
            writer.Write((short)0);    // File comment length
            writer.Write((short)0);    // Disk number start
            writer.Write((short)0);    // Internal file attributes
            writer.Write(0);           // External file attributes
            writer.Write(0);           // Relative offset of local header
            writer.Write(Encoding.UTF8.GetBytes("test.txt"));

            // ZIP end of central directory record
            writer.Write(0x06054b50); // End of central dir signature
            writer.Write((short)0);   // Number of this disk
            writer.Write((short)0);   // Number of disk with start of central directory
            writer.Write((short)1);   // Total number of entries in central directory on this disk
            writer.Write((short)1);   // Total number of entries in central directory
            writer.Write(46);         // Size of central directory
            writer.Write(30);         // Offset of start of central directory
            writer.Write((short)0);   // ZIP file comment length

            return memoryStream.ToArray();
        }

        #endregion
    }
}

using Bdo.Ess.CtsIntegration.Constants;
using Bdo.Ess.CtsIntegration.Entities;
using Bdo.Ess.CtsIntegration.enums;
using Bdo.Ess.CtsIntegration.StateMachine;
using Shouldly;
using System;
using System.Collections.Generic;
using System.Linq;
using Volo.Abp.Modularity;
using Xunit;

namespace Bdo.Ess.CtsIntegration.StateMachine
{
    public class DataPacketStateMachine_Tests : CtsIntegrationApplicationTestBase<CtsIntegrationApplicationTestModule>
    {
        #region Helper Methods

        private CtsPackageRequest CreateTestPackageRequest(CTSUploadStatus? uploadStatus = null, string transmissionStatus = "", DateTime? uploadedAt = null)
        {
            return new CtsPackageRequest(Guid.NewGuid())
            {
                UploadStatus = uploadStatus,
                TransmissionStatus = transmissionStatus,
                UploadedAt = uploadedAt,
                ReceiverCountryCode = "US",
                FiscalYear = 2023,
                TenantId = Guid.NewGuid(),
                IsExcludeCtsUpload = false,
                EligibleCheckCtsStatus = false
            };
        }

        #endregion

        #region TriggerEvent Tests

        [Fact]
        public void TriggerEvent_GenerationInProgress_CountryNotEnrolled_ShouldTransitionToReceivingCountryNotEnrolled()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.GenerationInProgress);

            // Act
            var result = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.CountryNotEnrolled);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.ReceivingCountryNotEnrolled);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.ReceivingCountryNotEnrolled);
            packageRequest.StatusUpdatedAt.ShouldNotBeNull();
        }

        [Fact]
        public void TriggerEvent_GenerationInProgress_GenerationFailed_ShouldTransitionToDataPacketGenerationFailed()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.GenerationInProgress);

            // Act
            var result = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.GenerationFailed);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.DataPacketGenerationFailed);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.DataPacketGenerationFailed);
        }

        [Fact]
        public void TriggerEvent_GenerationInProgress_GenerationSuccess_ShouldTransitionToNotStarted()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.GenerationInProgress);

            // Act
            var result = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.GenerationSuccess);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.NotStarted);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.NotStarted);
        }

        [Fact]
        public void TriggerEvent_NotStarted_StartUpload_ShouldTransitionToUploading()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.NotStarted);
            packageRequest.UploadedAt = DateTime.UtcNow; // Set initial value to verify it gets reset

            // Act
            var result = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.StartUpload);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.Uploading);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.Uploading);
            packageRequest.UploadedAt.ShouldBeNull(); // Should be reset on upload start
        }

        [Fact]
        public void TriggerEvent_NotStarted_MarkAsDoNotUpload_ShouldTransitionToDoNotUpload()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.NotStarted);

            // Act
            var result = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.MarkAsDoNotUpload);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.DoNotUpload);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.DoNotUpload);
            packageRequest.IsExcludeCtsUpload.ShouldBeTrue();
        }

        [Fact]
        public void TriggerEvent_DoNotUpload_UnmarkAsDoNotUpload_ShouldTransitionToNotStarted()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.DoNotUpload);
            packageRequest.IsExcludeCtsUpload = true;

            // Act
            var result = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.UnmarkAsDoNotUpload);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.NotStarted);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.NotStarted);
            packageRequest.IsExcludeCtsUpload.ShouldBeFalse();
        }

        [Fact]
        public void TriggerEvent_Uploading_UploadCompleted_ShouldTransitionToUploaded()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploading);

            // Act
            var result = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.UploadCompleted);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.Uploaded);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.Uploaded);
            packageRequest.EligibleCheckCtsStatus.ShouldBe(true);
            packageRequest.UploadedAt.ShouldNotBeNull();
            packageRequest.UploadedAt.Value.ShouldBeGreaterThan(DateTime.UtcNow.AddSeconds(-5));
            packageRequest.UploadedAt.Value.ShouldBeLessThan(DateTime.UtcNow.AddSeconds(5));
        }

        [Fact]
        public void TriggerEvent_Uploading_UploadFailed_ShouldTransitionToUploadFailed()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploading);

            // Act
            var result = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.UploadFailed);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.UploadFailed);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.UploadFailed);
        }

        [Fact]
        public void TriggerEvent_StartRegeneration_ShouldResetAllRelevantFields()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded);
            packageRequest.CtsPackageFileName = "test.zip";
            packageRequest.PackageZipUrl = "http://test.com/test.zip";
            packageRequest.UploadedAt = DateTime.UtcNow.AddDays(-1);
            packageRequest.EligibleCheckCtsStatus = true;
            packageRequest.IsExcludeCtsUpload = true;
            packageRequest.StatusUpdatedAt = DateTime.UtcNow.AddDays(-1);
            packageRequest.TransmissionStatus = "RC001";
            packageRequest.ProcessInfo = "Some process info";

            // Act
            var result = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.StartRegeneration);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.GenerationInProgress);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.GenerationInProgress);
            packageRequest.CtsPackageFileName.ShouldBeNull();
            packageRequest.PackageZipUrl.ShouldBeNull();
            packageRequest.UploadedAt.ShouldBeNull();
            packageRequest.EligibleCheckCtsStatus.ShouldBe(false);
            packageRequest.IsExcludeCtsUpload.ShouldBeFalse();
            packageRequest.StatusUpdatedAt.ShouldNotBeNull();
            packageRequest.TransmissionStatus.ShouldBe("");
            packageRequest.ProcessInfo.ShouldBeNull();
        }

        [Fact]
        public void TriggerEvent_InvalidTransition_ShouldReturnFailure()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploading);

            // Act
            var result = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.GenerationSuccess);

            // Assert
            result.IsSuccess.ShouldBeFalse();
            result.Message.ShouldContain("Invalid transition");
            result.NewState.ShouldBe(CTSUploadStatus.Uploading);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.Uploading); // Should remain unchanged
        }

        [Fact]
        public void TriggerEvent_NullUploadStatus_ShouldDefaultToGenerationInProgress()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(null);

            // Act
            var result = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.GenerationSuccess);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.NotStarted);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.NotStarted);
        }

        [Fact]
        public void TriggerEvent_TransmissionStatusReceived_ShouldUpdateEligibilityBasedOnRefreshLogic()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, "", DateTime.UtcNow.AddDays(-1));

            // Act & Assert
            // This should throw an exception because TransmissionStatusReceived is not properly handled in the state machine
            // The current implementation has a bug where it tries to access Transitions[transitionKey] even for TransmissionStatusReceived
            Should.Throw<System.Collections.Generic.KeyNotFoundException>(() =>
                DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.TransmissionStatusReceived));
        }

        #endregion

        #region GetAvailableActions Tests

        [Fact]
        public void GetAvailableActions_GenerationInProgress_ShouldReturnEmptyList()
        {
            // Act
            var actions = DataPacketStateMachine.GetAvailableActions(CTSUploadStatus.GenerationInProgress, "", null);

            // Assert
            actions.ShouldBeEmpty();
        }

        [Fact]
        public void GetAvailableActions_NotStarted_ShouldReturnCorrectActions()
        {
            // Act
            var actions = DataPacketStateMachine.GetAvailableActions(CTSUploadStatus.NotStarted, "", null);

            // Assert
            actions.ShouldContain(DataPacketAction.Upload);
            actions.ShouldContain(DataPacketAction.Regenerate);
            actions.ShouldContain(DataPacketAction.MarkDoNotUpload);
            actions.Count.ShouldBe(3);
        }

        [Fact]
        public void GetAvailableActions_Uploading_ShouldReturnEmptyList()
        {
            // Act
            var actions = DataPacketStateMachine.GetAvailableActions(CTSUploadStatus.Uploading, "", null);

            // Assert
            actions.ShouldBeEmpty();
        }

        [Fact]
        public void GetAvailableActions_Uploaded_ShouldReturnCorrectActions()
        {
            // Act
            var actions = DataPacketStateMachine.GetAvailableActions(CTSUploadStatus.Uploaded, "", DateTime.UtcNow);

            // Assert
            actions.ShouldContain(DataPacketAction.Regenerate);
            actions.ShouldContain(DataPacketAction.CheckTransmissionStatus);
            actions.Count.ShouldBe(2);
        }

        [Fact]
        public void GetAvailableActions_UploadFailed_ShouldReturnRegenerateOnly()
        {
            // Act
            var actions = DataPacketStateMachine.GetAvailableActions(CTSUploadStatus.UploadFailed, "", null);

            // Assert
            actions.ShouldContain(DataPacketAction.Regenerate);
            actions.Count.ShouldBe(1);
        }

        [Fact]
        public void GetAvailableActions_ReceivingCountryNotEnrolled_ShouldReturnRegenerateOnly()
        {
            // Act
            var actions = DataPacketStateMachine.GetAvailableActions(CTSUploadStatus.ReceivingCountryNotEnrolled, "", null);

            // Assert
            actions.ShouldContain(DataPacketAction.Regenerate);
            actions.Count.ShouldBe(1);
        }

        [Fact]
        public void GetAvailableActions_DataPacketGenerationFailed_ShouldReturnRegenerateOnly()
        {
            // Act
            var actions = DataPacketStateMachine.GetAvailableActions(CTSUploadStatus.DataPacketGenerationFailed, "", null);

            // Assert
            actions.ShouldContain(DataPacketAction.Regenerate);
            actions.Count.ShouldBe(1);
        }

        [Fact]
        public void GetAvailableActions_DoNotUpload_ShouldReturnUnmarkDoNotUploadOnly()
        {
            // Act
            var actions = DataPacketStateMachine.GetAvailableActions(CTSUploadStatus.DoNotUpload, "", null);

            // Assert
            actions.ShouldContain(DataPacketAction.UnmarkDoNotUpload);
            actions.Count.ShouldBe(1);
        }

        [Fact]
        public void GetAvailableActions_NullUploadStatus_ShouldDefaultToGenerationInProgress()
        {
            // Act
            var actions = DataPacketStateMachine.GetAvailableActions(null, "", null);

            // Assert
            actions.ShouldBeEmpty(); // GenerationInProgress has no available actions
        }

        #endregion

        #region CanPerformAction Tests

        [Fact]
        public void CanPerformAction_ValidAction_ShouldReturnTrue()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.NotStarted);

            // Act
            var canUpload = DataPacketStateMachine.CanPerformAction(packageRequest, DataPacketAction.Upload);
            var canRegenerate = DataPacketStateMachine.CanPerformAction(packageRequest, DataPacketAction.Regenerate);
            var canMarkDoNotUpload = DataPacketStateMachine.CanPerformAction(packageRequest, DataPacketAction.MarkDoNotUpload);

            // Assert
            canUpload.ShouldBeTrue();
            canRegenerate.ShouldBeTrue();
            canMarkDoNotUpload.ShouldBeTrue();
        }

        [Fact]
        public void CanPerformAction_InvalidAction_ShouldReturnFalse()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.GenerationInProgress);

            // Act
            var canUpload = DataPacketStateMachine.CanPerformAction(packageRequest, DataPacketAction.Upload);
            var canCheckStatus = DataPacketStateMachine.CanPerformAction(packageRequest, DataPacketAction.CheckTransmissionStatus);

            // Assert
            canUpload.ShouldBeFalse();
            canCheckStatus.ShouldBeFalse();
        }

        [Fact]
        public void CanPerformAction_DoNotUploadState_ShouldOnlyAllowUnmark()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.DoNotUpload);

            // Act
            var canUnmark = DataPacketStateMachine.CanPerformAction(packageRequest, DataPacketAction.UnmarkDoNotUpload);
            var canUpload = DataPacketStateMachine.CanPerformAction(packageRequest, DataPacketAction.Upload);
            var canRegenerate = DataPacketStateMachine.CanPerformAction(packageRequest, DataPacketAction.Regenerate);

            // Assert
            canUnmark.ShouldBeTrue();
            canUpload.ShouldBeFalse();
            canRegenerate.ShouldBeFalse();
        }

        #endregion

        #region IsTransmissionFinal Tests

        [Fact]
        public void IsTransmissionFinal_NotUploadedStatus_ShouldReturnFalse()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.NotStarted, "RC001", DateTime.UtcNow);

            // Act
            var isFinal = DataPacketStateMachine.IsTransmissionFinal(packageRequest);

            // Assert
            isFinal.ShouldBeFalse();
        }

        [Fact]
        public void IsTransmissionFinal_UploadedWithIntermediateStatus_ShouldReturnFalse()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, "RC001", DateTime.UtcNow);

            // Act
            var isFinal = DataPacketStateMachine.IsTransmissionFinal(packageRequest);

            // Assert
            isFinal.ShouldBeFalse();
        }

        [Theory]
        [InlineData("RC001")]
        [InlineData("RC007")]
        [InlineData("RC021")]
        [InlineData("RC022")]
        public void IsTransmissionFinal_UploadedWithIntermediateStatuses_ShouldReturnFalse(string transmissionStatus)
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, transmissionStatus, DateTime.UtcNow);

            // Act
            var isFinal = DataPacketStateMachine.IsTransmissionFinal(packageRequest);

            // Assert
            isFinal.ShouldBeFalse();
        }

        [Fact]
        public void IsTransmissionFinal_UploadedWithFinalStatus_ShouldReturnTrue()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, "RC024", DateTime.UtcNow);

            // Act
            var isFinal = DataPacketStateMachine.IsTransmissionFinal(packageRequest);

            // Assert
            isFinal.ShouldBeTrue();
        }

        [Fact]
        public void IsTransmissionFinal_UploadedOlderThan7Days_ShouldReturnTrue()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, "RC001", DateTime.UtcNow.AddDays(-8));

            // Act
            var isFinal = DataPacketStateMachine.IsTransmissionFinal(packageRequest);

            // Assert
            isFinal.ShouldBeTrue();
        }

        [Fact]
        public void IsTransmissionFinal_UploadedExactly7Days_ShouldReturnFalse()
        {
            // Arrange
            var uploadedAt = DateTime.UtcNow.AddDays(-7).AddMinutes(1); // Slightly less than 7 days to ensure it's not > 7
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, "RC001", uploadedAt);

            // Act
            var isFinal = DataPacketStateMachine.IsTransmissionFinal(packageRequest);

            // Assert
            isFinal.ShouldBeFalse();
        }

        [Fact]
        public void IsTransmissionFinal_UploadedWithNullTransmissionStatus_ShouldReturnTrue()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, "", DateTime.UtcNow);
            packageRequest.TransmissionStatus = null;

            // Act
            var isFinal = DataPacketStateMachine.IsTransmissionFinal(packageRequest);

            // Assert
            isFinal.ShouldBeTrue();
        }

        [Fact]
        public void IsTransmissionFinal_UploadedWithEmptyTransmissionStatus_ShouldReturnTrue()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, "", DateTime.UtcNow);

            // Act
            var isFinal = DataPacketStateMachine.IsTransmissionFinal(packageRequest);

            // Assert
            isFinal.ShouldBeTrue();
        }

        [Fact]
        public void IsTransmissionFinal_StaticMethod_ShouldWorkCorrectly()
        {
            // Act & Assert
            DataPacketStateMachine.IsTransmissionFinal(CTSUploadStatus.Uploaded, "RC024", DateTime.UtcNow).ShouldBeTrue();
            DataPacketStateMachine.IsTransmissionFinal(CTSUploadStatus.Uploaded, "RC001", DateTime.UtcNow).ShouldBeFalse();
            DataPacketStateMachine.IsTransmissionFinal(CTSUploadStatus.NotStarted, "RC024", DateTime.UtcNow).ShouldBeFalse();
            DataPacketStateMachine.IsTransmissionFinal(CTSUploadStatus.Uploaded, "RC001", DateTime.UtcNow.AddDays(-8)).ShouldBeTrue();
        }

        #endregion

        #region ShouldRefreshTransmissionStatus Tests

        [Fact]
        public void ShouldRefreshTransmissionStatus_UploadedWithEmptyTransmissionStatus_ShouldReturnTrue()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, "", DateTime.UtcNow);

            // Act
            var shouldRefresh = DataPacketStateMachine.ShouldRefreshTransmissionStatus(packageRequest);

            // Assert
            shouldRefresh.ShouldBeTrue();
        }

        [Fact]
        public void ShouldRefreshTransmissionStatus_UploadedWithNullTransmissionStatus_ShouldReturnTrue()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, "", DateTime.UtcNow);
            packageRequest.TransmissionStatus = null;

            // Act
            var shouldRefresh = DataPacketStateMachine.ShouldRefreshTransmissionStatus(packageRequest);

            // Assert
            shouldRefresh.ShouldBeTrue();
        }

        [Fact]
        public void ShouldRefreshTransmissionStatus_UploadedWithIntermediateStatus_ShouldReturnTrue()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, "RC001", DateTime.UtcNow);

            // Act
            var shouldRefresh = DataPacketStateMachine.ShouldRefreshTransmissionStatus(packageRequest);

            // Assert
            shouldRefresh.ShouldBeTrue();
        }

        [Fact]
        public void ShouldRefreshTransmissionStatus_UploadedWithFinalStatus_ShouldReturnFalse()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded, "RC024", DateTime.UtcNow);

            // Act
            var shouldRefresh = DataPacketStateMachine.ShouldRefreshTransmissionStatus(packageRequest);

            // Assert
            shouldRefresh.ShouldBeFalse();
        }

        [Fact]
        public void ShouldRefreshTransmissionStatus_NotUploadedStatus_ShouldReturnFalse()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.NotStarted, "", DateTime.UtcNow);

            // Act
            var shouldRefresh = DataPacketStateMachine.ShouldRefreshTransmissionStatus(packageRequest);

            // Assert
            shouldRefresh.ShouldBeFalse();
        }

        #endregion

        #region PerformActionAsync Tests

        [Fact]
        public void PerformActionAsync_ValidUploadAction_ShouldTriggerStartUploadEvent()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.NotStarted);

            // Act
            var result = DataPacketStateMachine.PerformActionAsync(packageRequest, DataPacketAction.Upload);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.Uploading);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.Uploading);
        }

        [Fact]
        public void PerformActionAsync_ValidRegenerateAction_ShouldTriggerStartRegenerationEvent()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.UploadFailed);

            // Act
            var result = DataPacketStateMachine.PerformActionAsync(packageRequest, DataPacketAction.Regenerate);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.GenerationInProgress);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.GenerationInProgress);
        }

        [Fact]
        public void PerformActionAsync_ValidMarkDoNotUploadAction_ShouldTriggerMarkAsDoNotUploadEvent()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.NotStarted);

            // Act
            var result = DataPacketStateMachine.PerformActionAsync(packageRequest, DataPacketAction.MarkDoNotUpload);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.DoNotUpload);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.DoNotUpload);
            packageRequest.IsExcludeCtsUpload.ShouldBeTrue();
        }

        [Fact]
        public void PerformActionAsync_ValidUnmarkDoNotUploadAction_ShouldTriggerUnmarkAsDoNotUploadEvent()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.DoNotUpload);

            // Act
            var result = DataPacketStateMachine.PerformActionAsync(packageRequest, DataPacketAction.UnmarkDoNotUpload);

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.NewState.ShouldBe(CTSUploadStatus.NotStarted);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.NotStarted);
            packageRequest.IsExcludeCtsUpload.ShouldBeFalse();
        }

        [Fact]
        public void PerformActionAsync_InvalidAction_ShouldReturnFailure()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.GenerationInProgress);

            // Act
            var result = DataPacketStateMachine.PerformActionAsync(packageRequest, DataPacketAction.Upload);

            // Assert
            result.IsSuccess.ShouldBeFalse();
            result.Message.ShouldContain("Action Upload is not available");
            result.NewState.ShouldBeNull(); // Special case for GenerationInProgress
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.GenerationInProgress); // Should remain unchanged
        }

        [Fact]
        public void PerformActionAsync_UnimplementedAction_ShouldReturnFailure()
        {
            // Arrange
            var packageRequest = CreateTestPackageRequest(CTSUploadStatus.Uploaded);

            // Act
            var result = DataPacketStateMachine.PerformActionAsync(packageRequest, DataPacketAction.CheckTransmissionStatus);

            // Assert
            result.IsSuccess.ShouldBeFalse();
            result.Message.ShouldContain("Action CheckTransmissionStatus is not implemented");
            result.NewState.ShouldBe(CTSUploadStatus.Uploaded);
            packageRequest.UploadStatus.ShouldBe(CTSUploadStatus.Uploaded); // Should remain unchanged
        }

        #endregion
    }
}
